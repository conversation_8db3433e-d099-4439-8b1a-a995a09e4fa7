var ee=Object.defineProperty;var ce=Object.getOwnPropertyDescriptor;var _e=Object.getOwnPropertyNames;var ae=Object.prototype.hasOwnProperty;var oe=(r,p)=>()=>(r&&(p=r(r=0)),p);var V=(r,p,W,b)=>{if(p&&typeof p=="object"||typeof p=="function")for(let x of _e(p))!ae.call(r,x)&&x!==W&&ee(r,x,{get:()=>p[x],enumerable:!(b=ce(p,x))||b.enumerable});return r},Y=(r,p,W)=>(V(r,p,"default"),W&&V(W,p,"default"));var re=r=>V(ee({},"__esModule",{value:!0}),r);var K={};import*as Mc from"async_hooks";var ne=oe(()=>{Y(K,Mc)});import{__getNamedExports as ie}from"../../../__next-on-pages-dist__/webpack/3251.js";import{__getNamedExports as ue}from"../../../__next-on-pages-dist__/webpack/4036.js";import{__getNamedExports as de}from"../../../__next-on-pages-dist__/webpack/8b7f424b706beefa6fe0dc8ca816b29f.js";import{__getNamedExports as he}from"../../../__next-on-pages-dist__/webpack/9068.js";import{__getNamedExports as me}from"../../../__next-on-pages-dist__/webpack/e4a1ac8d52c41560df3bf914bd0155f2.js";import{__getNamedExports as le}from"../../../__next-on-pages-dist__/webpack/1bf780904a25d17ad815e4754dc84ae6.js";import{__getNamedExports as ke}from"../../../__next-on-pages-dist__/webpack/7939.js";import{__getNamedExports as pe}from"../../../__next-on-pages-dist__/webpack/7fbb1437c7dfd5ff292a8d24d8d54a5f.js";import{__getNamedExports as xe}from"../../../__next-on-pages-dist__/webpack/e7664f1043d4c9bb684cf4a41a370f28.js";import{__getNamedExports as ge}from"../../../__next-on-pages-dist__/webpack/3318ff5949797c4e40b1d40e2f39ea09.js";import{__getNamedExports as fe}from"../../../__next-on-pages-dist__/webpack/815.js";import{__getNamedExports as ye}from"../../../__next-on-pages-dist__/webpack/5cc6287e1f68be9d6d8c5335ea861a39.js";import{__getNamedExports as be}from"../../../__next-on-pages-dist__/manifest/537259e2ed205f5a7994cb4f05664668.js";import{__getNamedExports as je}from"../../../__next-on-pages-dist__/manifest/__RSC_SERVER_MANIFEST.js";var a=globalThis.__nextOnPagesRoutesIsolation.getProxyFor("/domain/[domain]"),Ne=ie(a,a,a),ve=Ne.__chunk_3251,Ee=ue(a,a,a),Se=Ee.__chunk_4036,X=de(a,a,a),we=X.__chunk_525,Pe=X.__chunk_9335,De=X.__chunk_7290,Ce=X.__chunk_1413,Re=X.__chunk_4947,Oe=he(a,a,a),Te=Oe.__chunk_9068,y=me(a,a,a),Me=y.__chunk_3521,Ie=y.__chunk_6516,Ae=y.__chunk_6885,Fe=y.__chunk_4687,Le=y.__chunk_9576,Ue=y.__chunk_3531,$e=y.__chunk_8622,ze=y.__chunk_3376,Be=y.__chunk_7387,Ze=y.__chunk_4552,Ge=y.__chunk_454,We=y.__chunk_114,He=y.__chunk_1464,Ke=y.__chunk_5541,Xe=y.__chunk_6777,qe=y.__chunk_1370,Je=y.__chunk_5116,Ve=y.__chunk_8206,Ye=y.__chunk_3903,m=le(a,a,a),Qe=m.__chunk_6195,en=m.__chunk_2067,nn=m.__chunk_9182,tn=m.__chunk_8983,sn=m.__chunk_5228,cn=m.__chunk_2296,_n=m.__chunk_4101,an=m.__chunk_6776,on=m.__chunk_8042,rn=m.__chunk_3665,un=m.__chunk_6991,dn=m.__chunk_8816,hn=m.__chunk_6631,mn=m.__chunk_4828,ln=m.__chunk_828,kn=m.__chunk_5927,pn=m.__chunk_8439,xn=m.__chunk_4363,gn=m.__chunk_8264,fn=m.__chunk_1583,yn=m.__chunk_7908,bn=m.__chunk_8949,jn=m.__chunk_796,Nn=m.__chunk_1651,vn=m.__chunk_5105,En=m.__chunk_9642,Sn=m.__chunk_8819,wn=m.__chunk_676,Pn=ke(a,a,a),Dn=Pn.__chunk_7939,q=pe(a,a,a),Cn=q.__chunk_7884,Rn=q.__chunk_1166,On=q.__chunk_6050,Tn=q.__chunk_7084,Mn=q.__chunk_8620,G=xe(a,a,a),In=G.__chunk_733,An=G.__chunk_7133,Fn=G.__chunk_4125,Ln=G.__chunk_8016,Un=G.__chunk_2666,$n=G.__chunk_1929,zn=G.__chunk_5741,te=ge(a,a,a),Bn=te.__chunk_5285,Zn=te.__chunk_9839,Gn=fe(a,a,a),Wn=Gn.__chunk_815,n=ye(a,a,a),Hn=n.__chunk_4315,Kn=n.__chunk_8693,Xn=n.__chunk_3788,qn=n.__chunk_67,Jn=n.__chunk_9224,Vn=n.__chunk_8594,Yn=n.__chunk_1373,Qn=n.__chunk_8605,et=n.__chunk_5926,nt=n.__chunk_4258,tt=n.__chunk_3652,st=n.__chunk_939,ct=n.__chunk_3090,_t=n.__chunk_2324,at=n.__chunk_4630,ot=n.__chunk_6453,rt=n.__chunk_5505,it=n.__chunk_8797,ut=n.__chunk_473,dt=n.__chunk_2416,ht=n.__chunk_2168,mt=n.__chunk_5778,lt=n.__chunk_4814,kt=n.__chunk_5916,pt=n.__chunk_3774,xt=n.__chunk_2012,gt=n.__chunk_7506,ft=n.__chunk_8823,yt=n.__chunk_6083,bt=n.__chunk_9963,jt=n.__chunk_8215,Nt=n.__chunk_9558,vt=n.__chunk_7274,Et=n.__chunk_6037,St=n.__chunk_7843,wt=n.__chunk_4679,Pt=n.__chunk_8631,Dt=n.__chunk_9022,Ct=n.__chunk_4372,Rt=n.__chunk_1959,Ot=n.__chunk_9912,Tt=n.__chunk_402,Mt=n.__chunk_6419,It=n.__chunk_9951,At=n.__chunk_8027,Ft=n.__chunk_5975,Lt=n.__chunk_7105,Ut=n.__chunk_3889,$t=n.__chunk_5354,zt=n.__chunk_5918,Bt=n.__chunk_3786,Zt=n.__chunk_4504,Gt=n.__chunk_3589,Wt=n.__chunk_1958,Ht=n.__chunk_9556,Kt=n.__chunk_5374,Xt=n.__chunk_5291,qt=n.__chunk_3130,Jt=n.__chunk_6536,Vt=n.__chunk_7967,Yt=n.__chunk_1641,Qt=n.__chunk_3655,es=n.__chunk_3659,ns=n.__chunk_3395,ts=n.__chunk_6150,ss=n.__chunk_9141,cs=n.__chunk_7206,_s=n.__chunk_1575,as=n.__chunk_8269,os=n.__chunk_9319,rs=n.__chunk_9279,is=n.__chunk_2070,us=n.__chunk_2650,ds=n.__chunk_4009,hs=n.__chunk_7081,ms=n.__chunk_1427,ls=n.__chunk_1577,ks=n.__chunk_7514,ps=n.__chunk_1902,xs=n.__chunk_7538,gs=n.__chunk_2343,fs=n.__chunk_9567,ys=n.__chunk_9665,bs=n.__chunk_5358,js=n.__chunk_6512,Ns=n.__chunk_5787,vs=n.__chunk_4219,Es=n.__chunk_2042,Ss=n.__chunk_527,ws=n.__chunk_1057,Ps=n.__chunk_6588,Ds=n.__chunk_3000,Cs=n.__chunk_9094,Rs=n.__chunk_4439,Os=n.__chunk_7580,Ts=n.__chunk_3933,Ms=n.__chunk_6924,Is=n.__chunk_5694,As=n.__chunk_8906,Fs=n.__chunk_8359,Ls=n.__chunk_5912,Us=n.__chunk_9130,$s=n.__chunk_3433,zs=n.__chunk_4782,Bs=n.__chunk_7508,Zs=n.__chunk_8613,Gs=n.__chunk_7745,Ws=n.__chunk_7935,Hs=n.__chunk_7245,Ks=n.__chunk_7588,Xs=n.__chunk_1482,qs=n.__chunk_7603,Js=n.__chunk_3938,Vs=n.__chunk_9027,Ys=n.__chunk_3785,Qs=n.__chunk_849,ec=n.__chunk_7034,nc=n.__chunk_9338,tc=n.__chunk_5303,sc=n.__chunk_1514,cc=n.__chunk_518,_c=n.__chunk_2553,ac=n.__chunk_7106,oc=n.__chunk_5650,rc=n.__chunk_926,ic=n.__chunk_9220,uc=n.__chunk_2795,dc=n.__chunk_1112,hc=n.__chunk_2505,mc=n.__chunk_4174,lc=n.__chunk_8066,kc=n.__chunk_1880,pc=n.__chunk_6989,xc=n.__chunk_8004,gc=n.__chunk_7278,fc=n.__chunk_1982,yc=n.__chunk_4737,bc=n.__chunk_5080,jc=n.__chunk_3143,Nc=n.__chunk_9646,vc=n.__chunk_5181,Ec=n.__chunk_4164,Sc=n.__chunk_6287,wc=n.__chunk_8655,Pc=n.__chunk_9751,se=be(a,a,a),Dc=se.__NEXT_FONT_MANIFEST,Cc=se.__REACT_LOADABLE_MANIFEST,Rc=je(a,a,a),Oc=Rc.__RSC_SERVER_MANIFEST,qc=((r,p,W)=>(p._ENTRIES={},r.__RSC_SERVER_MANIFEST=Oc,p.__RSC_MANIFEST=p.__RSC_MANIFEST||{},p.__RSC_MANIFEST["/domain/[domain]/page"]={moduleLoading:{prefix:"/_next/",crossOrigin:null},ssrModuleMapping:{},edgeSSRModuleMapping:{80:{"*":{id:"7245",name:"*",chunks:[],async:!1}},702:{"*":{id:"1109",name:"*",chunks:[],async:!1}},1060:{"*":{id:"9027",name:"*",chunks:[],async:!1}},1956:{"*":{id:"5291",name:"*",chunks:[],async:!1}},2040:{"*":{id:"7582",name:"*",chunks:[],async:!1}},2513:{"*":{id:"9224",name:"*",chunks:[],async:!1}},2846:{"*":{id:"7034",name:"*",chunks:[],async:!1}},2972:{"*":{id:"6924",name:"*",chunks:[],async:!1}},4050:{"*":{id:"67",name:"*",chunks:[],async:!1}},4707:{"*":{id:"7603",name:"*",chunks:[],async:!1}},5154:{"*":{id:"9314",name:"*",chunks:[],async:!1}},5501:{"*":{id:"8823",name:"*",chunks:[],async:!1}},6045:{"*":{id:"9961",name:"*",chunks:[],async:!1}},6110:{"*":{id:"5975",name:"*",chunks:[],async:!1}},6423:{"*":{id:"4782",name:"*",chunks:[],async:!1}},8838:{"*":{id:"6019",name:"*",chunks:[],async:!1}},9060:{"*":{id:"4504",name:"*",chunks:[],async:!1}},9107:{"*":{id:"3785",name:"*",chunks:[],async:!1}},9698:{"*":{id:"2857",name:"*",chunks:[],async:!1}}},clientModules:{"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/app-router.js":{id:2846,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/app-router.js":{id:2846,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/client-page.js":{id:9107,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/client-page.js":{id:9107,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/error-boundary.js":{id:1060,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/error-boundary.js":{id:1060,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/layout-router.js":{id:4707,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/layout-router.js":{id:4707,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/not-found-boundary.js":{id:80,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/not-found-boundary.js":{id:80,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/render-from-template-context.js":{id:6423,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/render-from-template-context.js":{id:6423,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js":{id:1956,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/shared/lib/app-router-context.shared-runtime.js":{id:1956,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":{id:9060,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/shared/lib/hooks-client-context.shared-runtime.js":{id:9060,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js":{id:6110,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/shared/lib/loadable-context.shared-runtime.js":{id:6110,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js":{id:5501,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/shared/lib/server-inserted-html.shared-runtime.js":{id:5501,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/src/app/about/page.tsx":{id:8838,name:"*",chunks:[],async:!1},'/mnt/d/Demo/yuming/node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"variable":"--font-sans","display":"swap"}],"variableName":"inter"}':{id:7621,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},'/mnt/d/Demo/yuming/node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Playfair_Display","arguments":[{"subsets":["latin"],"variable":"--font-serif","display":"swap"}],"variableName":"playfair"}':{id:2714,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},'/mnt/d/Demo/yuming/node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"JetBrains_Mono","arguments":[{"subsets":["latin"],"variable":"--font-mono","display":"swap"}],"variableName":"jetbrains"}':{id:7736,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},"/mnt/d/Demo/yuming/src/components/theme-provider.tsx":{id:2513,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},"/mnt/d/Demo/yuming/src/contexts/TranslationContext.tsx":{id:4050,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},"/mnt/d/Demo/yuming/src/styles/globals.css":{id:9268,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/link.js":{id:2972,name:"*",chunks:["972","static/chunks/972-cdd33ba5872841df.js","160","static/chunks/app/not-found-ba19424f7a5329f4.js"],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/link.js":{id:2972,name:"*",chunks:["972","static/chunks/972-cdd33ba5872841df.js","160","static/chunks/app/not-found-ba19424f7a5329f4.js"],async:!1},"/mnt/d/Demo/yuming/src/app/privacy/page.tsx":{id:6045,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/src/app/page.tsx":{id:5154,name:"*",chunks:["670","static/chunks/670-5dd29538f44abde2.js","368","static/chunks/368-5a707ce5913f81e8.js","776","static/chunks/776-654c42c107014a82.js","821","static/chunks/821-de5aadc85bdb1304.js","85","static/chunks/85-5d406691b0db5af7.js","931","static/chunks/app/page-e748b76e7fffc821.js"],async:!1},"/mnt/d/Demo/yuming/src/app/domain/[domain]/page.tsx":{id:702,name:"*",chunks:["670","static/chunks/670-5dd29538f44abde2.js","972","static/chunks/972-cdd33ba5872841df.js","821","static/chunks/821-de5aadc85bdb1304.js","177","static/chunks/177-4a3bd049aa321cc3.js","616","static/chunks/app/domain/%5Bdomain%5D/page-e1a48028084cefb0.js"],async:!1},"/mnt/d/Demo/yuming/src/app/search/page.tsx":{id:2040,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/src/app/terms/page.tsx":{id:9698,name:"*",chunks:[],async:!1}},entryCSSFiles:{"/mnt/d/Demo/yuming/src/":[],"/mnt/d/Demo/yuming/src/app/layout":["static/css/dfba9f4c0a8ab98e.css"],"/mnt/d/Demo/yuming/src/app/not-found":[],"/mnt/d/Demo/yuming/src/app/page":[],"/mnt/d/Demo/yuming/src/app/domain/[domain]/page":[]}},r.__BUILD_MANIFEST={polyfillFiles:["static/chunks/polyfills-42372ed130431b0a.js"],devFiles:[],ampDevFiles:[],lowPriorityFiles:[],rootMainFiles:["static/chunks/webpack-08a9a15af710214c.js","static/chunks/fd9d1056-eabcefd8a17f0848.js","static/chunks/30-742145ca5a668fc1.js","static/chunks/main-app-1c9d1c6d6f88634c.js"],pages:{"/_app":["static/chunks/webpack-08a9a15af710214c.js","static/chunks/framework-f66176bb897dc684.js","static/chunks/main-bdc53fed2c1579dd.js","static/chunks/pages/_app-72b849fbd24ac258.js"],"/_error":["static/chunks/webpack-08a9a15af710214c.js","static/chunks/framework-f66176bb897dc684.js","static/chunks/main-bdc53fed2c1579dd.js","static/chunks/pages/_error-7ba65e1336b92748.js"]},ampFirstPages:[]},r.__BUILD_MANIFEST.lowPriorityFiles=["/static/L1ERtjXbxPN4Alx9vZK5J/_buildManifest.js",,"/static/L1ERtjXbxPN4Alx9vZK5J/_ssgManifest.js"],r.__REACT_LOADABLE_MANIFEST=Cc,r.__NEXT_FONT_MANIFEST=Dc,r.__INTERCEPTION_ROUTE_REWRITE_MANIFEST="[]",(()=>{"use strict";var b={},x={};function t(e){var i=x[e];if(i!==void 0)return i.exports;var _=x[e]={exports:{}},h=!0;try{b[e](_,_.exports,t),h=!1}finally{h&&delete x[e]}return _.exports}t.m=b,t.amdO={},(()=>{var e=[];t.O=(i,_,h,g)=>{if(_){g=g||0;for(var l=e.length;l>0&&e[l-1][2]>g;l--)e[l]=e[l-1];e[l]=[_,h,g];return}for(var f=1/0,l=0;l<e.length;l++){for(var[_,h,g]=e[l],N=!0,E=0;E<_.length;E++)f>=g&&Object.keys(t.O).every(A=>t.O[A](_[E]))?_.splice(E--,1):(N=!1,g<f&&(f=g));if(N){e.splice(l--,1);var d=h();d!==void 0&&(i=d)}}return i}})(),t.n=e=>{var i=e&&e.__esModule?()=>e.default:()=>e;return t.d(i,{a:i}),i},(()=>{var e,i=Object.getPrototypeOf?_=>Object.getPrototypeOf(_):_=>_.__proto__;t.t=function(_,h){if(1&h&&(_=this(_)),8&h||typeof _=="object"&&_&&(4&h&&_.__esModule||16&h&&typeof _.then=="function"))return _;var g=Object.create(null);t.r(g);var l={};e=e||[null,i({}),i([]),i(i)];for(var f=2&h&&_;typeof f=="object"&&!~e.indexOf(f);f=i(f))Object.getOwnPropertyNames(f).forEach(N=>l[N]=()=>_[N]);return l.default=()=>_,t.d(g,l),g}})(),t.d=(e,i)=>{for(var _ in i)t.o(i,_)&&!t.o(e,_)&&Object.defineProperty(e,_,{enumerable:!0,get:i[_]})},t.e=()=>Promise.resolve(),t.g=function(){if(typeof p=="object")return p;try{return this||Function("return this")()}catch{if(typeof window=="object")return window}}(),t.o=(e,i)=>Object.prototype.hasOwnProperty.call(e,i),t.r=e=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={993:0};t.O.j=h=>e[h]===0;var i=(h,g)=>{var l,f,[N,E,d]=g,O=0;if(N.some(B=>e[B]!==0)){for(l in E)t.o(E,l)&&(t.m[l]=E[l]);if(d)var z=d(t)}for(h&&h(g);O<N.length;O++)f=N[O],t.o(e,f)&&e[f]&&e[f][0](),e[f]=0;return t.O(z)},_=r.webpackChunk_N_E=r.webpackChunk_N_E||[];_.forEach(i.bind(null,0)),_.push=i.bind(null,_.push.bind(_))})()})(),(r.webpackChunk_N_E=r.webpackChunk_N_E||[]).push([[294],{676:wn,8819:Sn,9642:En,5105:vn,1651:Nn,796:jn,8949:bn,7908:yn,1583:fn,8264:gn,4363:xn,8439:pn,5927:kn,828:ln,4828:mn,6631:hn,8816:dn,6991:un,3665:rn,8042:on,6776:an,4101:_n,2296:cn,5228:sn,8983:tn,9182:nn}]),(r.webpackChunk_N_E=r.webpackChunk_N_E||[]).push([[714],{9751:Pc,8655:wc,6287:Sc,4164:Ec,5181:vc,9646:Nc,3143:jc,5080:bc,4737:yc,1982:fc,7278:gc,8004:xc,6989:pc,1880:kc,8066:lc,4174:mc,2505:hc,1112:dc,2795:uc,9220:ic,926:rc,5650:oc,7106:ac,2553:_c,518:cc,1514:sc,5303:tc,9338:nc,7034:ec,849:Qs,3785:Ys,9027:Vs,3938:Js,7603:qs,1482:Xs,7588:Ks,7245:Hs,7935:Ws,7745:Gs,8613:Zs,7508:Bs,4782:zs,3433:$s,9130:Us,5912:Ls,8359:Fs,8906:As,5694:Is,6924:Ms,3933:Ts,7580:Os,4439:Rs,9094:Cs,3e3:Ds,6588:Ps,1057:ws,527:Ss,2042:Es,4219:vs,5787:Ns,6512:js,5358:bs,9665:ys,9567:fs,2343:gs,7538:xs,1902:ps,7514:ks,1577:ls,1427:ms,7081:hs,4009:ds,2650:us,2070:is,9279:rs,9319:os,8269:as,1575:_s,7206:cs,9141:ss,6150:ts,3395:ns,3659:es,3655:Qt,1641:Yt,7967:Vt,6536:Jt,3130:qt,5291:Xt,5374:Kt,9556:Ht,1958:Wt,3589:Gt,4504:Zt,3786:Bt,5918:zt,5354:$t,3889:Ut,7105:Lt,5975:Ft,8027:At,9951:It,6419:Mt,402:Tt,9912:Ot,1959:Rt,4372:Ct,9022:Dt,8631:Pt,4679:wt,7843:St,6037:Et,7274:vt,9558:Nt,8215:jt,9963:bt,6083:yt,8823:ft,7506:gt,2012:xt,3774:pt,5916:kt,4814:lt,5778:mt,2168:ht,2416:dt,473:ut,8797:it,5505:rt,6453:ot,4630:at,2324:_t,3090:ct,939:st,3652:tt,4258:nt,5926:et}]),(r.webpackChunk_N_E=r.webpackChunk_N_E||[]).push([[243],{3903:Ye,8206:Ve,5116:Je,1370:qe,6777:Xe,5541:Ke,1464:He,114:We,454:Ge,4552:Ze,7387:Be,3376:ze,8622:$e,3531:Ue,9576:Le}]),(r.webpackChunk_N_E=r.webpackChunk_N_E||[]).push([[628],{8605:Qn,1373:Yn,8594:Vn,9224:Jn,67:qn,3788:Xn,8693:Kn,4315:Hn}]),(r.webpackChunk_N_E=r.webpackChunk_N_E||[]).push([[122],{815:Wn,5741:zn,8620:Mn,9839:Zn,1929:$n,2666:Un,5285:Bn,8016:Ln,4125:Fn,7133:An,7084:Tn,6050:On,1166:Rn,733:In,7884:Cn,7939:Dn}]),(r.webpackChunk_N_E=r.webpackChunk_N_E||[]).push([[616],{2067:en,6195:Qe,9692:(b,x,t)=>{"use strict";t.r(x),t.d(x,{ComponentMod:()=>c,default:()=>o});var e,i={};t.r(i),t.d(i,{AppRouter:()=>d.WY,ClientPageRoot:()=>d.b1,GlobalError:()=>E.ZP,LayoutRouter:()=>d.yO,NotFoundBoundary:()=>d.O4,Postpone:()=>d.hQ,RenderFromTemplateContext:()=>d.b5,__next_app__:()=>Z,actionAsyncStorage:()=>d.Wz,createDynamicallyTrackedSearchParams:()=>d.rL,createUntrackedSearchParams:()=>d.S5,decodeAction:()=>d.Hs,decodeFormState:()=>d.dH,decodeReply:()=>d.kf,originalPathname:()=>B,pages:()=>z,patchFetch:()=>d.XH,preconnect:()=>d.$P,preloadFont:()=>d.C5,preloadStyle:()=>d.oH,renderToReadableStream:()=>d.aW,requestAsyncStorage:()=>d.Fg,routeModule:()=>A,serverHooks:()=>d.GP,staticGenerationAsyncStorage:()=>d.AT,taintObjectReference:()=>d.nr,tree:()=>O}),t(7206);var _=t(9319),h=t(518),g=t(1902),l=t(2042),f=t(4630),N=t(4828),E=t(5505),d=t(6453);let O=["",{children:["domain",{children:["[domain]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5701)),"/mnt/d/Demo/yuming/src/app/domain/[domain]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,3788)),"/mnt/d/Demo/yuming/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,8693)),"/mnt/d/Demo/yuming/src/app/not-found.tsx"]}],z=["/mnt/d/Demo/yuming/src/app/domain/[domain]/page.tsx"],B="/domain/[domain]/page",Z={require:t,loadChunk:()=>Promise.resolve()},A=new f.AppPageRouteModule({definition:{kind:N.x.APP_PAGE,page:"/domain/[domain]/page",pathname:"/domain/[domain]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:O}});var C=t(9094),S=t(5787),R=t(527);let F=v=>v?JSON.parse(v):void 0,H=r.__BUILD_MANIFEST,$=F(r.__REACT_LOADABLE_MANIFEST),P=(e=r.__RSC_MANIFEST)==null?void 0:e["/domain/[domain]/page"],L=F(r.__RSC_SERVER_MANIFEST),D=F(r.__NEXT_FONT_MANIFEST),s=F(r.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];P&&L&&(0,S.Mo)({clientReferenceManifest:P,serverActionsManifest:L,serverModuleMap:(0,R.w)({serverActionsManifest:L,pageName:"/domain/[domain]/page"})});let u=(0,h.d)({pagesType:C.s.APP,dev:!1,page:"/domain/[domain]/page",appMod:null,pageMod:i,errorMod:null,error500Mod:null,Document:null,buildManifest:H,renderToHTML:l.f,reactLoadableManifest:$,clientReferenceManifest:P,serverActionsManifest:L,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{RDAP_CACHE_TTL:"3600",RDAP_TIMEOUT:"2500"},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.js",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:604800,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!0,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:15,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"/mnt/d/Demo/yuming",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"/mnt/d/Demo/yuming/next.config.js",configFileName:"next.config.js",compiler:{removeConsole:{exclude:["error","warn"]}}},buildId:"L1ERtjXbxPN4Alx9vZK5J",nextFontManifest:D,incrementalCacheHandler:null,interceptionRouteRewrites:s}),c=i;function o(v){return(0,_.C)({...v,IncrementalCache:g.k,handler:u})}},5388:(b,x,t)=>{Promise.resolve().then(t.bind(t,1109))},4947:Re,1109:(b,x,t)=>{"use strict";t.r(x),t.d(x,{default:()=>s,runtime:()=>L});var e=t(926),i=t(9220),_=t(4947),h=t(1464),g=t(5285),l=t(8016),f=t(4125),N=t(2666),E=t(733),d=t(3251),O=t(1413),z=t(3531),B=t(525),Z=t(7884),A=t(815),C=t(4687),S=t(6885),R=t(8620),F=t(1929),H=t(5741),$=t(9839),P=t(6516);let L="edge";function D(){let u=(0,_.UO)().domain,[c,o]=(0,i.useState)(null),[v,j]=(0,i.useState)(!0),[U,T]=(0,i.useState)(!1),{t:w}=(0,P.T)(),M=async()=>{j(!0);try{setTimeout(()=>{let k={domain:decodeURIComponent(u),isAvailable:Math.random()>.5,whoisInfo:{domainName:decodeURIComponent(u),registryDomainId:"D123456789-LROR",registrarWhoisServer:"whois.markmonitor.com",registrarUrl:"http://www.markmonitor.com",updatedDate:"2019-09-09T15:39:04Z",creationDate:"1997-09-15T04:00:00Z",registryExpiryDate:"2028-09-14T04:00:00Z",registrar:"MarkMonitor Inc.",registrarIanaId:"292",registrarAbuseContactEmail:"<EMAIL>",registrarAbuseContactPhone:"*************",domainStatus:["clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited","clientTransferProhibited https://icann.org/epp#clientTransferProhibited","clientUpdateProhibited https://icann.org/epp#clientUpdateProhibited"],nameServers:["NS1.GOOGLE.COM","NS2.GOOGLE.COM","NS3.GOOGLE.COM","NS4.GOOGLE.COM"],dnssec:"unsigned",lastUpdateOfWhoisDatabase:"2025-07-10T09:52:50Z"},prices:[{registrar:"GoDaddy",logo:"/logos/godaddy.svg",registrationPrice:12.99,renewalPrice:17.99,transferPrice:12.99,currency:"USD",specialOffer:"\u9996\u5E74\u7279\u4EF7",rating:4.2,features:["\u514D\u8D39WHOIS\u9690\u79C1","24/7\u5BA2\u670D","\u57DF\u540D\u8F6C\u53D1"],affiliateLink:"https://godaddy.com"},{registrar:"Namecheap",logo:"/logos/namecheap.svg",registrationPrice:10.69,renewalPrice:13.99,transferPrice:10.69,currency:"USD",rating:4.5,features:["\u514D\u8D39WHOIS\u9690\u79C1","DNS\u7BA1\u7406","\u90AE\u7BB1\u8F6C\u53D1"],affiliateLink:"https://namecheap.com"},{registrar:"Cloudflare",logo:"/logos/cloudflare.svg",registrationPrice:8.57,renewalPrice:8.57,transferPrice:8.57,currency:"USD",rating:4.8,features:["\u6279\u53D1\u4EF7\u683C","\u514D\u8D39SSL","DNSSEC"],affiliateLink:"https://cloudflare.com"},{registrar:"Porkbun",logo:"/logos/porkbun.svg",registrationPrice:9.13,renewalPrice:11.98,transferPrice:9.13,currency:"USD",rating:4.6,features:["\u514D\u8D39WHOIS\u9690\u79C1","\u514D\u8D39SSL","API\u8BBF\u95EE"],affiliateLink:"https://porkbun.com"},{registrar:"Google Domains",logo:"/logos/google.svg",registrationPrice:12,renewalPrice:12,transferPrice:12,currency:"USD",rating:4.3,features:["Google\u96C6\u6210","\u514D\u8D39\u9690\u79C1\u4FDD\u62A4","\u7B80\u6D01\u754C\u9762"],affiliateLink:"https://domains.google"}],recommendations:[`${decodeURIComponent(u).split(".")[0]}.net`,`${decodeURIComponent(u).split(".")[0]}.org`,`${decodeURIComponent(u).split(".")[0]}.io`,`get${decodeURIComponent(u).split(".")[0]}.com`,`${decodeURIComponent(u).split(".")[0]}app.com`]};o(k),j(!1)},1e3)}catch(k){console.error("Failed to fetch domain detail:",k),j(!1)}},J=k=>{navigator.clipboard.writeText(k),T(!0),setTimeout(()=>T(!1),2e3)};return v?(0,e.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-background to-secondary/20",children:(0,e.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,e.jsx)("div",{className:"mb-8",children:(0,e.jsx)(A.Z,{href:"/search",children:(0,e.jsxs)(C.z,{variant:"ghost",size:"sm",children:[(0,e.jsx)(g.Z,{className:"w-4 h-4 mr-2"}),"\u8FD4\u56DE\u641C\u7D22"]})})}),(0,e.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,e.jsxs)("div",{className:"text-center space-y-4",children:[(0,e.jsx)(F.ii,{size:"lg"}),(0,e.jsx)("p",{className:"text-lg font-medium",children:w("domain.loadingDetails")}),(0,e.jsxs)("p",{className:"text-sm text-muted-foreground",children:["\u6B63\u5728\u67E5\u8BE2: ",decodeURIComponent(u)]})]})})]})}):c?(0,e.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-background to-secondary/20",children:(0,e.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,e.jsx)("div",{className:"mb-8",children:(0,e.jsx)(A.Z,{href:"/search",children:(0,e.jsxs)(C.z,{variant:"ghost",size:"sm",children:[(0,e.jsx)(g.Z,{className:"w-4 h-4 mr-2"}),"\u8FD4\u56DE\u641C\u7D22"]})})}),(0,e.jsx)($.uo,{children:(0,e.jsxs)(h.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"glass-card p-8 rounded-3xl mb-8 relative overflow-hidden",children:[(0,e.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 to-purple-500/5"}),(0,e.jsx)("div",{className:"relative z-10",children:(0,e.jsxs)("div",{className:"flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6",children:[(0,e.jsxs)("div",{className:"space-y-4",children:[(0,e.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,e.jsx)("h1",{className:"text-4xl font-bold font-mono text-primary",children:c.domain}),(0,e.jsx)(C.z,{variant:"ghost",size:"sm",onClick:()=>J(c.domain),className:"opacity-70 hover:opacity-100",children:U?(0,e.jsx)(l.Z,{className:"h-4 w-4"}):(0,e.jsx)(f.Z,{className:"h-4 w-4"})})]}),(0,e.jsx)("div",{className:"flex items-center space-x-2",children:c.isAvailable?(0,e.jsxs)(R.C,{className:"bg-green-100 text-green-800 border-green-200 px-4 py-2",children:[(0,e.jsx)(l.Z,{className:"h-4 w-4 mr-2"}),"\u53EF\u4EE5\u6CE8\u518C"]}):(0,e.jsxs)(R.C,{variant:"secondary",className:"bg-red-100 text-red-800 border-red-200 px-4 py-2",children:[(0,e.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"\u5DF2\u88AB\u6CE8\u518C"]})}),c.isAvailable&&c.prices&&(0,e.jsxs)("div",{className:"text-lg",children:["\u6700\u4F4E\u6CE8\u518C\u4EF7\u683C:",(0,e.jsxs)("span",{className:"font-bold text-green-600 ml-2",children:["$",Math.min(...c.prices.map(k=>k.registrationPrice))]}),(0,e.jsxs)("span",{className:"text-sm text-muted-foreground ml-2",children:["(",c.prices.find(k=>k.registrationPrice===Math.min(...c.prices.map(I=>I.registrationPrice)))?.registrar,")"]})]})]}),c.isAvailable&&(0,e.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,e.jsxs)(C.z,{size:"lg",className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,e.jsx)(E.Z,{className:"mr-2 h-5 w-5"}),"\u7ACB\u5373\u6CE8\u518C"]}),(0,e.jsxs)(C.z,{size:"lg",variant:"outline",children:[(0,e.jsx)(d.Z,{className:"mr-2 h-5 w-5"}),"\u6536\u85CF\u57DF\u540D"]})]})]})})]})}),c.isAvailable&&c.prices&&(0,e.jsx)(h.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"mb-8",children:(0,e.jsx)($.JS,{children:(0,e.jsxs)(S.Zb,{children:[(0,e.jsx)(S.Ol,{children:(0,e.jsxs)(S.ll,{className:"flex items-center space-x-2",children:[(0,e.jsx)(O.Z,{className:"h-6 w-6 text-primary"}),(0,e.jsx)("span",{children:"\u6CE8\u518C\u5546\u4EF7\u683C\u5BF9\u6BD4"})]})}),(0,e.jsx)(S.aY,{children:(0,e.jsx)("div",{className:"overflow-x-auto",children:(0,e.jsxs)("table",{className:"w-full",children:[(0,e.jsx)("thead",{children:(0,e.jsxs)("tr",{className:"border-b",children:[(0,e.jsx)("th",{className:"text-left py-3 px-4",children:"\u6CE8\u518C\u5546"}),(0,e.jsx)("th",{className:"text-center py-3 px-4",children:"\u8BC4\u5206"}),(0,e.jsx)("th",{className:"text-center py-3 px-4",children:"\u9996\u5E74\u6CE8\u518C"}),(0,e.jsx)("th",{className:"text-center py-3 px-4",children:"\u7EED\u8D39\u4EF7\u683C"}),(0,e.jsx)("th",{className:"text-center py-3 px-4",children:"\u8F6C\u5165\u4EF7\u683C"}),(0,e.jsx)("th",{className:"text-center py-3 px-4",children:"\u7279\u8272\u529F\u80FD"}),(0,e.jsx)("th",{className:"text-center py-3 px-4",children:"\u64CD\u4F5C"})]})}),(0,e.jsx)("tbody",{children:c.prices.sort((k,I)=>k.registrationPrice-I.registrationPrice).map((k,I)=>(0,e.jsxs)(h.E.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*I},className:`border-b hover:bg-muted/50 transition-colors ${I===0?"bg-green-50 dark:bg-green-900/20":""}`,children:[(0,e.jsx)("td",{className:"py-4 px-4",children:(0,e.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,e.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded flex items-center justify-center",children:(0,e.jsx)(z.Z,{className:"h-4 w-4"})}),(0,e.jsxs)("div",{children:[(0,e.jsx)("div",{className:"font-semibold",children:k.registrar}),k.specialOffer&&(0,e.jsx)(R.C,{variant:"secondary",className:"text-xs",children:k.specialOffer})]})]})}),(0,e.jsx)("td",{className:"text-center py-4 px-4",children:(0,e.jsxs)("div",{className:"flex items-center justify-center space-x-1",children:[(0,e.jsx)(d.Z,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,e.jsx)("span",{className:"font-medium",children:k.rating})]})}),(0,e.jsx)("td",{className:"text-center py-4 px-4",children:(0,e.jsxs)("div",{className:"font-bold text-lg",children:["$",k.registrationPrice,I===0&&(0,e.jsx)(R.C,{className:"ml-2 bg-green-100 text-green-800",children:"\u6700\u4F4E\u4EF7"})]})}),(0,e.jsx)("td",{className:"text-center py-4 px-4",children:(0,e.jsxs)("span",{className:"text-muted-foreground",children:["$",k.renewalPrice]})}),(0,e.jsx)("td",{className:"text-center py-4 px-4",children:(0,e.jsxs)("span",{className:"text-muted-foreground",children:["$",k.transferPrice]})}),(0,e.jsx)("td",{className:"py-4 px-4",children:(0,e.jsxs)("div",{className:"flex flex-wrap gap-1 justify-center",children:[k.features.slice(0,2).map(Q=>(0,e.jsx)(R.C,{variant:"outline",className:"text-xs",children:Q},Q)),k.features.length>2&&(0,e.jsxs)(R.C,{variant:"outline",className:"text-xs",children:["+",k.features.length-2]})]})}),(0,e.jsx)("td",{className:"text-center py-4 px-4",children:(0,e.jsxs)(C.z,{size:"sm",className:I===0?"bg-green-600 hover:bg-green-700":"",children:[(0,e.jsx)(B.Z,{className:"mr-2 h-4 w-4"}),"\u6CE8\u518C"]})})]},k.registrar))})]})})})]})})}),!c.isAvailable&&c.whoisInfo&&(0,e.jsx)(h.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"mb-8",children:(0,e.jsx)(H.U,{domain:c.domain,whoisInfo:c.whoisInfo,isAvailable:c.isAvailable})}),c.recommendations&&c.recommendations.length>0&&(0,e.jsx)(h.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:(0,e.jsx)($.JS,{children:(0,e.jsxs)(S.Zb,{children:[(0,e.jsx)(S.Ol,{children:(0,e.jsxs)(S.ll,{className:"flex items-center space-x-2",children:[(0,e.jsx)(Z.Z,{className:"h-6 w-6 text-primary"}),(0,e.jsx)("span",{children:"\u76F8\u5173\u57DF\u540D\u63A8\u8350"})]})}),(0,e.jsx)(S.aY,{children:(0,e.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:c.recommendations.map((k,I)=>(0,e.jsx)(h.E.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.1*I},children:(0,e.jsx)($.JS,{children:(0,e.jsx)(S.Zb,{className:"cursor-pointer hover:shadow-md transition-all duration-200 border-2 border-transparent hover:border-primary/30",children:(0,e.jsxs)(S.aY,{className:"p-4",children:[(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsx)("span",{className:"font-mono font-semibold",children:k}),(0,e.jsx)(R.C,{className:"bg-green-100 text-green-800",children:"\u53EF\u6CE8\u518C"})]}),(0,e.jsx)("div",{className:"mt-2 text-sm text-muted-foreground",children:"\u9884\u4F30\u4EF7\u683C: $12.99"})]})})})},k))})})]})})})]})}):(0,e.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-background to-secondary/20",children:(0,e.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,e.jsx)("div",{className:"text-center py-20",children:(0,e.jsxs)("div",{className:"space-y-4",children:[(0,e.jsx)("div",{className:"text-6xl",children:"\u274C"}),(0,e.jsx)("h3",{className:"text-xl font-semibold",children:w("domain.loadingFailed")}),(0,e.jsx)("p",{className:"text-muted-foreground",children:w("errors.pleaseTryAgain")}),(0,e.jsx)(C.z,{onClick:M,children:w("common.reload")})]})})})})}function s(){return(0,e.jsx)(i.Suspense,{fallback:(0,e.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-background to-secondary/20 flex items-center justify-center",children:(0,e.jsxs)("div",{className:"text-center space-y-4",children:[(0,e.jsx)(F.ii,{size:"lg"}),(0,e.jsx)("p",{className:"text-lg font-medium",children:"\u52A0\u8F7D\u4E2D..."})]})}),children:(0,e.jsx)(D,{})})}},4687:Fe,6885:Ae,6516:Ie,3521:Me,5701:(b,x,t)=>{"use strict";t.r(x),t.d(x,{default:()=>_,runtime:()=>i});var e=t(8264);let i=(0,e.D)(String.raw`/mnt/d/Demo/yuming/src/app/domain/[domain]/page.tsx#runtime`),_=(0,e.D)(String.raw`/mnt/d/Demo/yuming/src/app/domain/[domain]/page.tsx#default`)},1413:Ce,7290:De,9068:Te,9335:Pe,525:we,4036:Se,3251:ve},b=>{var x=e=>b(b.s=e);b.O(0,[294,714,243,628,122],()=>x(9692));var t=b.O();(p._ENTRIES=typeof p._ENTRIES>"u"?{}:p._ENTRIES)["middleware_app/domain/[domain]/page"]=t}]),function(){let b={exports:{},loaded:!1};return function(t,e){var i=Object.create,_=Object.defineProperty,h=Object.getOwnPropertyDescriptor,g=Object.getOwnPropertyNames,l=Object.getPrototypeOf,f=Object.prototype.hasOwnProperty,N=s=>_(s,"__esModule",{value:!0}),E=(s,u)=>{N(s);for(var c in u)_(s,c,{get:u[c],enumerable:!0})},d=(s,u,c)=>{if(u&&typeof u=="object"||typeof u=="function")for(let o of g(u))!f.call(s,o)&&o!=="default"&&_(s,o,{get:()=>u[o],enumerable:!(c=h(u,o))||c.enumerable});return s},O=s=>d(N(_(s!=null?i(l(s)):{},"default",s&&s.__esModule&&"default"in s?{get:()=>s.default,enumerable:!0}:{value:s,enumerable:!0})),s);E(e,{default:()=>$});var z=O((ne(),re(K))),B="@next/request-context",Z=Symbol.for(B),A=Symbol.for("internal.storage");function C(){let s=p;if(!s[Z]){let u=new z.AsyncLocalStorage,c={get:()=>u.getStore(),[A]:u};s[Z]=c}return s[Z]}var S=C();function R(s,u){return S[A].run(s,u)}function F(s){let u={};return s&&s.forEach((c,o)=>{u[o]=c,o.toLowerCase()==="set-cookie"&&(u[o]=H(c))}),u}function H(s){let u=[],c=0,o,v,j,U,T;function w(){for(;c<s.length&&/\s/.test(s.charAt(c));)c+=1;return c<s.length}function M(){return v=s.charAt(c),v!=="="&&v!==";"&&v!==","}for(;c<s.length;){for(o=c,T=!1;w();)if(v=s.charAt(c),v===","){for(j=c,c+=1,w(),U=c;c<s.length&&M();)c+=1;c<s.length&&s.charAt(c)==="="?(T=!0,c=U,u.push(s.substring(o,j)),o=c):c=j+1}else c+=1;(!T||c>=s.length)&&u.push(s.substring(o,s.length))}return u}function $(s){let u=s.staticRoutes.map(o=>({regexp:new RegExp(o.namedRegex),page:o.page})),c=s.dynamicRoutes?.map(o=>({regexp:new RegExp(o.namedRegex),page:o.page}))||[];return async function(o,v){let j=new URL(o.url).pathname,U={};if(s.nextConfig?.basePath&&j.startsWith(s.nextConfig.basePath)&&(j=j.replace(s.nextConfig.basePath,"")||"/"),s.nextConfig?.i18n)for(let w of s.nextConfig.i18n.locales){let M=new RegExp(`^/${w}($|/)`,"i");if(j.match(M)){j=j.replace(M,"/")||"/";break}}for(let w of u)if(w.regexp.exec(j)){U.name=w.page;break}if(!U.name){let w=L(j);for(let M of c||[]){if(w&&!L(M.page))continue;let J=M.regexp.exec(j);if(J){U={name:M.page,params:J.groups};break}}}let T=await R({waitUntil:v.waitUntil},()=>p._ENTRIES[`middleware_${s.name}`].default.call({},{request:{url:o.url,method:o.method,headers:F(o.headers),ip:P(o.headers,D.Ip),geo:{city:P(o.headers,D.City,!0),country:P(o.headers,D.Country,!0),latitude:P(o.headers,D.Latitude),longitude:P(o.headers,D.Longitude),region:P(o.headers,D.Region,!0)},nextConfig:s.nextConfig,page:U,body:o.body}}));return T.waitUntil&&v.waitUntil(T.waitUntil),T.response}}function P(s,u,c=!1){let o=s.get(u)||void 0;return c&&o?decodeURIComponent(o):o}function L(s){return s==="/api"||s.startsWith("/api/")}var D;(function(s){s.City="x-vercel-ip-city",s.Country="x-vercel-ip-country",s.Ip="x-real-ip",s.Latitude="x-vercel-ip-latitude",s.Longitude="x-vercel-ip-longitude",s.Region="x-vercel-ip-country-region"})(D||(D={}))}(b,b.exports),b.exports}.call({}).default({name:"app/domain/[domain]/page",staticRoutes:[{page:"/",regex:"^/(?:/)?$",routeKeys:{},namedRegex:"^/(?:/)?$"},{page:"/_not-found",regex:"^/_not\\-found(?:/)?$",routeKeys:{},namedRegex:"^/_not\\-found(?:/)?$"},{page:"/about",regex:"^/about(?:/)?$",routeKeys:{},namedRegex:"^/about(?:/)?$"},{page:"/privacy",regex:"^/privacy(?:/)?$",routeKeys:{},namedRegex:"^/privacy(?:/)?$"},{page:"/search",regex:"^/search(?:/)?$",routeKeys:{},namedRegex:"^/search(?:/)?$"},{page:"/sitemap.xml",regex:"^/sitemap\\.xml(?:/)?$",routeKeys:{},namedRegex:"^/sitemap\\.xml(?:/)?$"},{page:"/terms",regex:"^/terms(?:/)?$",routeKeys:{},namedRegex:"^/terms(?:/)?$"}],dynamicRoutes:[{page:"/api/domain/[domain]",regex:"^/api/domain/([^/]+?)(?:/)?$",routeKeys:{nxtPdomain:"nxtPdomain"},namedRegex:"^/api/domain/(?<nxtPdomain>[^/]+?)(?:/)?$"},{page:"/domain/[domain]",regex:"^/domain/([^/]+?)(?:/)?$",routeKeys:{nxtPdomain:"nxtPdomain"},namedRegex:"^/domain/(?<nxtPdomain>[^/]+?)(?:/)?$"}],nextConfig:{basePath:""}})))(a,a,a);export{qc as default};
