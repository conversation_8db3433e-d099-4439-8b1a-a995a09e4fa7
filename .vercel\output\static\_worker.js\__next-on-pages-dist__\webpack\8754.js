var r={},v=(p,u,x)=>(r.__chunk_8754=(g,c,o)=>{"use strict";o.d(c,{F_:()=>n,ZU:()=>d,jx:()=>m,r$:()=>l});class h{set(t,s,e=9e5){this.cache.size>=this.maxSize&&(this.evictExpired(),this.cache.size>=this.maxSize&&this.evictLRU()),this.cache.set(t,{data:s,timestamp:Date.now(),expiry:Date.now()+e,hits:0}),this.stats.sets++}get(t){let s=this.cache.get(t);return s?Date.now()>s.expiry?(this.cache.delete(t),this.stats.misses++,null):(s.hits++,this.stats.hits++,s.data):(this.stats.misses++,null)}has(t){let s=this.cache.get(t);return!!s&&(!(Date.now()>s.expiry)||(this.cache.delete(t),!1))}delete(t){return this.cache.delete(t)}clear(){this.cache.clear(),this.resetStats()}evictExpired(){let t=Date.now(),s=0;for(let[e,a]of Array.from(this.cache.entries()))t>a.expiry&&(this.cache.delete(e),s++);this.stats.evictions+=s}evictLRU(){let t=Array.from(this.cache.entries()),s=Math.floor(.1*t.length);t.sort((e,a)=>e[1].hits===a[1].hits?e[1].timestamp-a[1].timestamp:e[1].hits-a[1].hits);for(let e=0;e<s;e++)this.cache.delete(t[e][0]),this.stats.evictions++}getStats(){let t=this.stats.hits/(this.stats.hits+this.stats.misses)||0;return{...this.stats,hitRate:Math.round(100*t),size:this.cache.size,maxSize:this.maxSize}}resetStats(){this.stats.hits=0,this.stats.misses=0,this.stats.sets=0,this.stats.evictions=0}getInfo(){return{...this.getStats(),memoryUsage:this.estimateMemoryUsage()}}estimateMemoryUsage(){let t=1024*Array.from(this.cache.values()).length;return t<1024?`${t} B`:t<1048576?`${Math.round(t/1024)} KB`:`${Math.round(t/1048576)} MB`}constructor(){this.cache=new Map,this.maxSize=1e3,this.stats={hits:0,misses:0,sets:0,evictions:0}}}let n=new h,l=new h,m=new h,d={domain:i=>`domain:${i.toLowerCase()}`,search:(i,t,s=1)=>`search:${i}:${t}:${s}`,tldList:()=>"tlds:list",rdapServers:i=>`rdap:${i}`,priceData:i=>`price:${i}`,popularDomains:()=>"popular:domains",stats:()=>"cache:stats"}},r);export{v as __getNamedExports};
