var As=(Bt=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(Bt,{get:($r,Zr)=>(typeof require<"u"?require:$r)[Zr]}):Bt)(function(Bt){if(typeof require<"u")return require.apply(this,arguments);throw new Error('Dynamic require of "'+Bt+'" is not supported')});var Pe={},Vs=(Bt,$r,Zr)=>(Pe.__chunk_3521=(Ve,Y,k)=>{"use strict";k.d(Y,{cn:()=>G});var P=k(5116),Z=k(9576);function G(...ne){return(0,Z.m6)((0,P.W)(ne))}},Pe.__chunk_6516=(Ve,Y,k)=>{"use strict";k.d(Y,{T:()=>P.T});var P=k(67)},Pe.__chunk_6885=(Ve,Y,k)=>{"use strict";k.d(Y,{Ol:()=>ee,SZ:()=>q,Zb:()=>ne,aY:()=>W,ll:()=>de});var P=k(926),Z=k(9220),G=k(3521);let ne=Z.forwardRef(({className:B,...N},U)=>(0,P.jsx)("div",{ref:U,className:(0,G.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",B),...N}));ne.displayName="Card";let ee=Z.forwardRef(({className:B,...N},U)=>(0,P.jsx)("div",{ref:U,className:(0,G.cn)("flex flex-col space-y-1.5 p-6",B),...N}));ee.displayName="CardHeader";let de=Z.forwardRef(({className:B,...N},U)=>(0,P.jsx)("h3",{ref:U,className:(0,G.cn)("text-2xl font-semibold leading-none tracking-tight",B),...N}));de.displayName="CardTitle";let q=Z.forwardRef(({className:B,...N},U)=>(0,P.jsx)("p",{ref:U,className:(0,G.cn)("text-sm text-muted-foreground",B),...N}));q.displayName="CardDescription";let W=Z.forwardRef(({className:B,...N},U)=>(0,P.jsx)("div",{ref:U,className:(0,G.cn)("p-6 pt-0",B),...N}));W.displayName="CardContent",Z.forwardRef(({className:B,...N},U)=>(0,P.jsx)("div",{ref:U,className:(0,G.cn)("flex items-center p-6 pt-0",B),...N})).displayName="CardFooter"},Pe.__chunk_4687=(Ve,Y,k)=>{"use strict";k.d(Y,{z:()=>q});var P=k(926),Z=k(9220),G=k(3903),ne=k(8206),ee=k(3521);let de=(0,ne.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),q=Z.forwardRef(({className:W,variant:B,size:N,asChild:U=!1,...J},oe)=>{let ce=U?G.g7:"button";return(0,P.jsx)(ce,{className:(0,ee.cn)(de({variant:B,size:N,className:W})),ref:oe,...J})});q.displayName="Button"},Pe.__chunk_9576=(Ve,Y,k)=>{k.d(Y,{m6:()=>Ue});let P=m=>{let A=ee(m),{conflictingClassGroups:V,conflictingClassGroupModifiers:j}=m;return{getClassGroupId:O=>{let L=O.split("-");return L[0]===""&&L.length!==1&&L.shift(),Z(L,A)||ne(O)},getConflictingClassGroupIds:(O,L)=>{let X=V[O]||[];return L&&j[O]?[...X,...j[O]]:X}}},Z=(m,A)=>{if(m.length===0)return A.classGroupId;let V=m[0],j=A.nextPart.get(V),O=j?Z(m.slice(1),j):void 0;if(O)return O;if(A.validators.length===0)return;let L=m.join("-");return A.validators.find(({validator:X})=>X(L))?.classGroupId},G=/^\[(.+)\]$/,ne=m=>{if(G.test(m)){let A=G.exec(m)[1],V=A?.substring(0,A.indexOf(":"));if(V)return"arbitrary.."+V}},ee=m=>{let{theme:A,prefix:V}=m,j={nextPart:new Map,validators:[]};return B(Object.entries(m.classGroups),V).forEach(([O,L])=>{de(L,j,O,A)}),j},de=(m,A,V,j)=>{m.forEach(O=>{if(typeof O=="string"){(O===""?A:q(A,O)).classGroupId=V;return}if(typeof O=="function"){if(W(O)){de(O(j),A,V,j);return}A.validators.push({validator:O,classGroupId:V});return}Object.entries(O).forEach(([L,X])=>{de(X,q(A,L),V,j)})})},q=(m,A)=>{let V=m;return A.split("-").forEach(j=>{V.nextPart.has(j)||V.nextPart.set(j,{nextPart:new Map,validators:[]}),V=V.nextPart.get(j)}),V},W=m=>m.isThemeGetter,B=(m,A)=>A?m.map(([V,j])=>[V,j.map(O=>typeof O=="string"?A+O:typeof O=="object"?Object.fromEntries(Object.entries(O).map(([L,X])=>[A+L,X])):O)]):m,N=m=>{if(m<1)return{get:()=>{},set:()=>{}};let A=0,V=new Map,j=new Map,O=(L,X)=>{V.set(L,X),++A>m&&(A=0,j=V,V=new Map)};return{get(L){let X=V.get(L);return X!==void 0?X:(X=j.get(L))!==void 0?(O(L,X),X):void 0},set(L,X){V.has(L)?V.set(L,X):O(L,X)}}},U=m=>{let{separator:A,experimentalParseClassName:V}=m,j=A.length===1,O=A[0],L=A.length,X=$=>{let Ce,De=[],Ne=0,Ie=0;for(let ue=0;ue<$.length;ue++){let xe=$[ue];if(Ne===0){if(xe===O&&(j||$.slice(ue,ue+L)===A)){De.push($.slice(Ie,ue)),Ie=ue+L;continue}if(xe==="/"){Ce=ue;continue}}xe==="["?Ne++:xe==="]"&&Ne--}let Oe=De.length===0?$:$.substring(Ie),He=Oe.startsWith("!"),_e=He?Oe.substring(1):Oe;return{modifiers:De,hasImportantModifier:He,baseClassName:_e,maybePostfixModifierPosition:Ce&&Ce>Ie?Ce-Ie:void 0}};return V?$=>V({className:$,parseClassName:X}):X},J=m=>{if(m.length<=1)return m;let A=[],V=[];return m.forEach(j=>{j[0]==="["?(A.push(...V.sort(),j),V=[]):V.push(j)}),A.push(...V.sort()),A},oe=m=>({cache:N(m.cacheSize),parseClassName:U(m),...P(m)}),ce=/\s+/,te=(m,A)=>{let{parseClassName:V,getClassGroupId:j,getConflictingClassGroupIds:O}=A,L=[],X=m.trim().split(ce),$="";for(let Ce=X.length-1;Ce>=0;Ce-=1){let De=X[Ce],{modifiers:Ne,hasImportantModifier:Ie,baseClassName:Oe,maybePostfixModifierPosition:He}=V(De),_e=!!He,ue=j(_e?Oe.substring(0,He):Oe);if(!ue){if(!_e||!(ue=j(Oe))){$=De+($.length>0?" "+$:$);continue}_e=!1}let xe=J(Ne).join(":"),Re=Ie?xe+"!":xe,Le=Re+ue;if(L.includes(Le))continue;L.push(Le);let lt=O(ue,_e);for(let Qe=0;Qe<lt.length;++Qe){let ut=lt[Qe];L.push(Re+ut)}$=De+($.length>0?" "+$:$)}return $};function ve(){let m,A,V=0,j="";for(;V<arguments.length;)(m=arguments[V++])&&(A=K(m))&&(j&&(j+=" "),j+=A);return j}let K=m=>{let A;if(typeof m=="string")return m;let V="";for(let j=0;j<m.length;j++)m[j]&&(A=K(m[j]))&&(V&&(V+=" "),V+=A);return V},z=m=>{let A=V=>V[m]||[];return A.isThemeGetter=!0,A},Se=/^\[(?:([a-z-]+):)?(.+)\]$/i,be=/^\d+\/\d+$/,Me=new Set(["px","full","screen"]),je=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Te=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,fe=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,pe=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ge=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,re=m=>le(m)||Me.has(m)||be.test(m),ke=m=>nt(m,"length",dr),le=m=>!!m&&!Number.isNaN(Number(m)),st=m=>nt(m,"number",le),rt=m=>!!m&&Number.isInteger(Number(m)),Xt=m=>m.endsWith("%")&&le(m.slice(0,-1)),I=m=>Se.test(m),ze=m=>je.test(m),hr=new Set(["length","size","percentage"]),qe=m=>nt(m,hr,at),it=m=>nt(m,"position",at),yt=new Set(["image","url"]),Ct=m=>nt(m,yt,Je),vt=m=>nt(m,"",bt),Ke=()=>!0,nt=(m,A,V)=>{let j=Se.exec(m);return!!j&&(j[1]?typeof A=="string"?j[1]===A:A.has(j[1]):V(j[2]))},dr=m=>Te.test(m)&&!fe.test(m),at=()=>!1,bt=m=>pe.test(m),Je=m=>ge.test(m),Ue=function(m,...A){let V,j,O,L=function($){return j=(V=oe(A.reduce((Ce,De)=>De(Ce),m()))).cache.get,O=V.cache.set,L=X,X($)};function X($){let Ce=j($);if(Ce)return Ce;let De=te($,V);return O($,De),De}return function(){return L(ve.apply(null,arguments))}}(()=>{let m=z("colors"),A=z("spacing"),V=z("blur"),j=z("brightness"),O=z("borderColor"),L=z("borderRadius"),X=z("borderSpacing"),$=z("borderWidth"),Ce=z("contrast"),De=z("grayscale"),Ne=z("hueRotate"),Ie=z("invert"),Oe=z("gap"),He=z("gradientColorStops"),_e=z("gradientColorStopPositions"),ue=z("inset"),xe=z("margin"),Re=z("opacity"),Le=z("padding"),lt=z("saturate"),Qe=z("scale"),ut=z("sepia"),Ot=z("skew"),At=z("space"),qt=z("translate"),ht=()=>["auto","contain","none"],Nt=()=>["auto","hidden","clip","visible","scroll"],It=()=>["auto",I,A],se=()=>[I,A],Wt=()=>["",re,ke],D=()=>["auto",le,I],Kt=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Et=()=>["solid","dashed","dotted","double","none"],$e=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Vt=()=>["start","end","center","between","around","evenly","stretch"],et=()=>["","0",I],Jt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Ae=()=>[le,I];return{cacheSize:500,separator:":",theme:{colors:[Ke],spacing:[re,ke],blur:["none","",ze,I],brightness:Ae(),borderColor:[m],borderRadius:["none","","full",ze,I],borderSpacing:se(),borderWidth:Wt(),contrast:Ae(),grayscale:et(),hueRotate:Ae(),invert:et(),gap:se(),gradientColorStops:[m],gradientColorStopPositions:[Xt,ke],inset:It(),margin:It(),opacity:Ae(),padding:se(),saturate:Ae(),scale:Ae(),sepia:et(),skew:Ae(),space:se(),translate:se()},classGroups:{aspect:[{aspect:["auto","square","video",I]}],container:["container"],columns:[{columns:[ze]}],"break-after":[{"break-after":Jt()}],"break-before":[{"break-before":Jt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Kt(),I]}],overflow:[{overflow:Nt()}],"overflow-x":[{"overflow-x":Nt()}],"overflow-y":[{"overflow-y":Nt()}],overscroll:[{overscroll:ht()}],"overscroll-x":[{"overscroll-x":ht()}],"overscroll-y":[{"overscroll-y":ht()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[ue]}],"inset-x":[{"inset-x":[ue]}],"inset-y":[{"inset-y":[ue]}],start:[{start:[ue]}],end:[{end:[ue]}],top:[{top:[ue]}],right:[{right:[ue]}],bottom:[{bottom:[ue]}],left:[{left:[ue]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",rt,I]}],basis:[{basis:It()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",I]}],grow:[{grow:et()}],shrink:[{shrink:et()}],order:[{order:["first","last","none",rt,I]}],"grid-cols":[{"grid-cols":[Ke]}],"col-start-end":[{col:["auto",{span:["full",rt,I]},I]}],"col-start":[{"col-start":D()}],"col-end":[{"col-end":D()}],"grid-rows":[{"grid-rows":[Ke]}],"row-start-end":[{row:["auto",{span:[rt,I]},I]}],"row-start":[{"row-start":D()}],"row-end":[{"row-end":D()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",I]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",I]}],gap:[{gap:[Oe]}],"gap-x":[{"gap-x":[Oe]}],"gap-y":[{"gap-y":[Oe]}],"justify-content":[{justify:["normal",...Vt()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...Vt(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...Vt(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[Le]}],px:[{px:[Le]}],py:[{py:[Le]}],ps:[{ps:[Le]}],pe:[{pe:[Le]}],pt:[{pt:[Le]}],pr:[{pr:[Le]}],pb:[{pb:[Le]}],pl:[{pl:[Le]}],m:[{m:[xe]}],mx:[{mx:[xe]}],my:[{my:[xe]}],ms:[{ms:[xe]}],me:[{me:[xe]}],mt:[{mt:[xe]}],mr:[{mr:[xe]}],mb:[{mb:[xe]}],ml:[{ml:[xe]}],"space-x":[{"space-x":[At]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[At]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",I,A]}],"min-w":[{"min-w":[I,A,"min","max","fit"]}],"max-w":[{"max-w":[I,A,"none","full","min","max","fit","prose",{screen:[ze]},ze]}],h:[{h:[I,A,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[I,A,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[I,A,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[I,A,"auto","min","max","fit"]}],"font-size":[{text:["base",ze,ke]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",st]}],"font-family":[{font:[Ke]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",I]}],"line-clamp":[{"line-clamp":["none",le,st]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",re,I]}],"list-image":[{"list-image":["none",I]}],"list-style-type":[{list:["none","disc","decimal",I]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[m]}],"placeholder-opacity":[{"placeholder-opacity":[Re]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[m]}],"text-opacity":[{"text-opacity":[Re]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Et(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",re,ke]}],"underline-offset":[{"underline-offset":["auto",re,I]}],"text-decoration-color":[{decoration:[m]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:se()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",I]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",I]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[Re]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Kt(),it]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",qe]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Ct]}],"bg-color":[{bg:[m]}],"gradient-from-pos":[{from:[_e]}],"gradient-via-pos":[{via:[_e]}],"gradient-to-pos":[{to:[_e]}],"gradient-from":[{from:[He]}],"gradient-via":[{via:[He]}],"gradient-to":[{to:[He]}],rounded:[{rounded:[L]}],"rounded-s":[{"rounded-s":[L]}],"rounded-e":[{"rounded-e":[L]}],"rounded-t":[{"rounded-t":[L]}],"rounded-r":[{"rounded-r":[L]}],"rounded-b":[{"rounded-b":[L]}],"rounded-l":[{"rounded-l":[L]}],"rounded-ss":[{"rounded-ss":[L]}],"rounded-se":[{"rounded-se":[L]}],"rounded-ee":[{"rounded-ee":[L]}],"rounded-es":[{"rounded-es":[L]}],"rounded-tl":[{"rounded-tl":[L]}],"rounded-tr":[{"rounded-tr":[L]}],"rounded-br":[{"rounded-br":[L]}],"rounded-bl":[{"rounded-bl":[L]}],"border-w":[{border:[$]}],"border-w-x":[{"border-x":[$]}],"border-w-y":[{"border-y":[$]}],"border-w-s":[{"border-s":[$]}],"border-w-e":[{"border-e":[$]}],"border-w-t":[{"border-t":[$]}],"border-w-r":[{"border-r":[$]}],"border-w-b":[{"border-b":[$]}],"border-w-l":[{"border-l":[$]}],"border-opacity":[{"border-opacity":[Re]}],"border-style":[{border:[...Et(),"hidden"]}],"divide-x":[{"divide-x":[$]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[$]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[Re]}],"divide-style":[{divide:Et()}],"border-color":[{border:[O]}],"border-color-x":[{"border-x":[O]}],"border-color-y":[{"border-y":[O]}],"border-color-s":[{"border-s":[O]}],"border-color-e":[{"border-e":[O]}],"border-color-t":[{"border-t":[O]}],"border-color-r":[{"border-r":[O]}],"border-color-b":[{"border-b":[O]}],"border-color-l":[{"border-l":[O]}],"divide-color":[{divide:[O]}],"outline-style":[{outline:["",...Et()]}],"outline-offset":[{"outline-offset":[re,I]}],"outline-w":[{outline:[re,ke]}],"outline-color":[{outline:[m]}],"ring-w":[{ring:Wt()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[m]}],"ring-opacity":[{"ring-opacity":[Re]}],"ring-offset-w":[{"ring-offset":[re,ke]}],"ring-offset-color":[{"ring-offset":[m]}],shadow:[{shadow:["","inner","none",ze,vt]}],"shadow-color":[{shadow:[Ke]}],opacity:[{opacity:[Re]}],"mix-blend":[{"mix-blend":[...$e(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":$e()}],filter:[{filter:["","none"]}],blur:[{blur:[V]}],brightness:[{brightness:[j]}],contrast:[{contrast:[Ce]}],"drop-shadow":[{"drop-shadow":["","none",ze,I]}],grayscale:[{grayscale:[De]}],"hue-rotate":[{"hue-rotate":[Ne]}],invert:[{invert:[Ie]}],saturate:[{saturate:[lt]}],sepia:[{sepia:[ut]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[V]}],"backdrop-brightness":[{"backdrop-brightness":[j]}],"backdrop-contrast":[{"backdrop-contrast":[Ce]}],"backdrop-grayscale":[{"backdrop-grayscale":[De]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Ne]}],"backdrop-invert":[{"backdrop-invert":[Ie]}],"backdrop-opacity":[{"backdrop-opacity":[Re]}],"backdrop-saturate":[{"backdrop-saturate":[lt]}],"backdrop-sepia":[{"backdrop-sepia":[ut]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[X]}],"border-spacing-x":[{"border-spacing-x":[X]}],"border-spacing-y":[{"border-spacing-y":[X]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",I]}],duration:[{duration:Ae()}],ease:[{ease:["linear","in","out","in-out",I]}],delay:[{delay:Ae()}],animate:[{animate:["none","spin","ping","pulse","bounce",I]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[Qe]}],"scale-x":[{"scale-x":[Qe]}],"scale-y":[{"scale-y":[Qe]}],rotate:[{rotate:[rt,I]}],"translate-x":[{"translate-x":[qt]}],"translate-y":[{"translate-y":[qt]}],"skew-x":[{"skew-x":[Ot]}],"skew-y":[{"skew-y":[Ot]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",I]}],accent:[{accent:["auto",m]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",I]}],"caret-color":[{caret:[m]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":se()}],"scroll-mx":[{"scroll-mx":se()}],"scroll-my":[{"scroll-my":se()}],"scroll-ms":[{"scroll-ms":se()}],"scroll-me":[{"scroll-me":se()}],"scroll-mt":[{"scroll-mt":se()}],"scroll-mr":[{"scroll-mr":se()}],"scroll-mb":[{"scroll-mb":se()}],"scroll-ml":[{"scroll-ml":se()}],"scroll-p":[{"scroll-p":se()}],"scroll-px":[{"scroll-px":se()}],"scroll-py":[{"scroll-py":se()}],"scroll-ps":[{"scroll-ps":se()}],"scroll-pe":[{"scroll-pe":se()}],"scroll-pt":[{"scroll-pt":se()}],"scroll-pr":[{"scroll-pr":se()}],"scroll-pb":[{"scroll-pb":se()}],"scroll-pl":[{"scroll-pl":se()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",I]}],fill:[{fill:[m,"none"]}],"stroke-w":[{stroke:[re,ke,st]}],stroke:[{stroke:[m,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},Pe.__chunk_3531=(Ve,Y,k)=>{k.d(Y,{Z:()=>P});let P=(0,k(8622).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["path",{d:"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z",key:"nb9nel"}]])},Pe.__chunk_8622=(Ve,Y,k)=>{k.d(Y,{Z:()=>ne});var P=k(9220),Z={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let G=ee=>ee.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var ne=(ee,de)=>{let q=(0,P.forwardRef)(({color:W="currentColor",size:B=24,strokeWidth:N=2,absoluteStrokeWidth:U,children:J,...oe},ce)=>(0,P.createElement)("svg",{ref:ce,...Z,width:B,height:B,stroke:W,strokeWidth:U?24*Number(N)/Number(B):N,className:`lucide lucide-${G(ee)}`,...oe},[...de.map(([te,ve])=>(0,P.createElement)(te,ve)),...(Array.isArray(J)?J:[J])||[]]));return q.displayName=`${ee}`,q}},Pe.__chunk_3376=(Ve,Y,k)=>{k.d(Y,{L:()=>Z});var P=k(9220);let Z=k(454).j?P.useLayoutEffect:P.useEffect},Pe.__chunk_7387=(Ve,Y,k)=>{k.d(Y,{h:()=>Z});var P=k(9220);function Z(G){let ne=(0,P.useRef)(null);return ne.current===null&&(ne.current=G()),ne.current}},Pe.__chunk_4552=(Ve,Y,k)=>{k.d(Y,{Z:()=>P});let P=Z=>Z},Pe.__chunk_454=(Ve,Y,k)=>{k.d(Y,{j:()=>P});let P=typeof document<"u"},Pe.__chunk_114=(Ve,Y,k)=>{k.d(Y,{K:()=>Z,k:()=>G});var P=k(4552);let Z=P.Z,G=P.Z},Pe.__chunk_1464=(Ve,Y,k)=>{k.d(Y,{E:()=>Cs});var P=k(9220);let Z=(0,P.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),G=(0,P.createContext)({});var ne=k(6777),ee=k(3376);let de=(0,P.createContext)({strict:!1}),q=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),W="data-"+q("framerAppearId");function B(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function N(e){return typeof e=="string"||Array.isArray(e)}function U(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}let J=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],oe=["initial",...J];function ce(e){return U(e.animate)||oe.some(t=>N(e[t]))}function te(e){return!!(ce(e)||e.variants)}function ve(e){return Array.isArray(e)?e.join(" "):e}let K={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},z={};for(let e in K)z[e]={isEnabled:t=>K[e].some(r=>!!t[r])};var Se=k(454),be=k(1370);let Me=(0,P.createContext)({}),je=Symbol.for("motionComponentSymbol"),Te=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function fe(e){if(!(typeof e!="string"||e.includes("-"))){if(Te.indexOf(e)>-1||/[A-Z]/.test(e))return!0}return!1}let pe={},ge=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],re=new Set(ge);function ke(e,{layout:t,layoutId:r}){return re.has(e)||e.startsWith("origin")||(t||r!==void 0)&&(!!pe[e]||e==="opacity")}let le=e=>!!(e&&e.getVelocity),st={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rt=ge.length,Xt=e=>t=>typeof t=="string"&&t.startsWith(e),I=Xt("--"),ze=Xt("var(--"),hr=(e,t)=>t&&typeof e=="number"?t.transform(e):e,qe=(e,t,r)=>Math.min(Math.max(r,e),t),it={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},yt={...it,transform:e=>qe(0,1,e)},Ct={...it,default:1},vt=e=>Math.round(1e5*e)/1e5,Ke=/(-)?([\d]*\.?[\d])+/g,nt=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,dr=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function at(e){return typeof e=="string"}let bt=e=>({test:t=>at(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Je=bt("deg"),Ue=bt("%"),m=bt("px"),A=bt("vh"),V=bt("vw"),j={...Ue,parse:e=>Ue.parse(e)/100,transform:e=>Ue.transform(100*e)},O={...it,transform:Math.round},L={borderWidth:m,borderTopWidth:m,borderRightWidth:m,borderBottomWidth:m,borderLeftWidth:m,borderRadius:m,radius:m,borderTopLeftRadius:m,borderTopRightRadius:m,borderBottomRightRadius:m,borderBottomLeftRadius:m,width:m,maxWidth:m,height:m,maxHeight:m,size:m,top:m,right:m,bottom:m,left:m,padding:m,paddingTop:m,paddingRight:m,paddingBottom:m,paddingLeft:m,margin:m,marginTop:m,marginRight:m,marginBottom:m,marginLeft:m,rotate:Je,rotateX:Je,rotateY:Je,rotateZ:Je,scale:Ct,scaleX:Ct,scaleY:Ct,scaleZ:Ct,skew:Je,skewX:Je,skewY:Je,distance:m,translateX:m,translateY:m,translateZ:m,x:m,y:m,z:m,perspective:m,transformPerspective:m,opacity:yt,originX:j,originY:j,originZ:m,zIndex:O,fillOpacity:yt,strokeOpacity:yt,numOctaves:O};function X(e,t,r,i){let{style:a,vars:n,transform:o,transformOrigin:s}=e,l=!1,u=!1,d=!0;for(let h in t){let p=t[h];if(I(h)){n[h]=p;continue}let c=L[h],f=hr(p,c);if(re.has(h)){if(l=!0,o[h]=f,!d)continue;p!==(c.default||0)&&(d=!1)}else h.startsWith("origin")?(u=!0,s[h]=f):a[h]=f}if(!t.transform&&(l||i?a.transform=function(h,{enableHardwareAcceleration:p=!0,allowTransformNone:c=!0},f,v){let g="";for(let w=0;w<rt;w++){let b=ge[w];if(h[b]!==void 0){let y=st[b]||b;g+=`${y}(${h[b]}) `}}return p&&!h.z&&(g+="translateZ(0)"),g=g.trim(),v?g=v(h,f?"":g):c&&f&&(g="none"),g}(e.transform,r,d,i):a.transform&&(a.transform="none")),u){let{originX:h="50%",originY:p="50%",originZ:c=0}=s;a.transformOrigin=`${h} ${p} ${c}`}}let $=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ce(e,t,r){for(let i in t)le(t[i])||ke(i,r)||(e[i]=t[i])}let De=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ne(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||De.has(e)}let Ie=e=>!Ne(e);try{(function(e){e&&(Ie=t=>t.startsWith("on")?!Ne(t):e(t))})(As("@emotion/is-prop-valid").default)}catch{}function Oe(e,t,r){return typeof e=="string"?e:m.transform(t+r*e)}let He={offset:"stroke-dashoffset",array:"stroke-dasharray"},_e={offset:"strokeDashoffset",array:"strokeDasharray"};function ue(e,{attrX:t,attrY:r,attrScale:i,originX:a,originY:n,pathLength:o,pathSpacing:s=1,pathOffset:l=0,...u},d,h,p){if(X(e,u,d,p),h){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:c,style:f,dimensions:v}=e;c.transform&&(v&&(f.transform=c.transform),delete c.transform),v&&(a!==void 0||n!==void 0||f.transform)&&(f.transformOrigin=function(g,w,b){let y=Oe(w,g.x,g.width),x=Oe(b,g.y,g.height);return`${y} ${x}`}(v,a!==void 0?a:.5,n!==void 0?n:.5)),t!==void 0&&(c.x=t),r!==void 0&&(c.y=r),i!==void 0&&(c.scale=i),o!==void 0&&function(g,w,b=1,y=0,x=!0){g.pathLength=1;let T=x?He:_e;g[T.offset]=m.transform(-y);let C=m.transform(w),E=m.transform(b);g[T.array]=`${C} ${E}`}(c,o,s,l,!1)}let xe=()=>({...$(),attrs:{}}),Re=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Le(e,{style:t,vars:r},i,a){for(let n in Object.assign(e.style,t,a&&a.getProjectionStyles(i)),r)e.style.setProperty(n,r[n])}let lt=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Qe(e,t,r,i){for(let a in Le(e,t,void 0,i),t.attrs)e.setAttribute(lt.has(a)?a:q(a),t.attrs[a])}function ut(e,t){let{style:r}=e,i={};for(let a in r)(le(r[a])||t.style&&le(t.style[a])||ke(a,e))&&(i[a]=r[a]);return i}function Ot(e,t){let r=ut(e,t);for(let i in e)(le(e[i])||le(t[i]))&&(r[ge.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return r}function At(e,t,r,i={},a={}){return typeof t=="function"&&(t=t(r!==void 0?r:e.custom,i,a)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(r!==void 0?r:e.custom,i,a)),t}var qt=k(7387);let ht=e=>Array.isArray(e),Nt=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),It=e=>ht(e)?e[e.length-1]||0:e;function se(e){let t=le(e)?e.get():e;return Nt(t)?t.toValue():t}let Wt=e=>(t,r)=>{let i=(0,P.useContext)(G),a=(0,P.useContext)(ne.O),n=()=>function({scrapeMotionValuesFromProps:o,createRenderState:s,onMount:l},u,d,h){let p={latestValues:function(c,f,v,g){let w={},b=g(c,{});for(let F in b)w[F]=se(b[F]);let{initial:y,animate:x}=c,T=ce(c),C=te(c);f&&C&&!T&&c.inherit!==!1&&(y===void 0&&(y=f.initial),x===void 0&&(x=f.animate));let E=!!v&&v.initial===!1,S=(E=E||y===!1)?x:y;return S&&typeof S!="boolean"&&!U(S)&&(Array.isArray(S)?S:[S]).forEach(F=>{let Q=At(c,F);if(!Q)return;let{transitionEnd:he,transition:ie,..._}=Q;for(let M in _){let H=_[M];if(Array.isArray(H)){let R=E?H.length-1:0;H=H[R]}H!==null&&(w[M]=H)}for(let M in he)w[M]=he[M]}),w}(u,d,h,o),renderState:s()};return l&&(p.mount=c=>l(u,c,p)),p}(e,t,i,a);return r?n():(0,qt.h)(n)};var D=k(5541);let Kt={useVisualState:Wt({scrapeMotionValuesFromProps:Ot,createRenderState:xe,onMount:(e,t,{renderState:r,latestValues:i})=>{D.Wi.read(()=>{try{r.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{r.dimensions={x:0,y:0,width:0,height:0}}}),D.Wi.render(()=>{ue(r,i,{enableHardwareAcceleration:!1},Re(t.tagName),e.transformTemplate),Qe(t,r)})}})},Et={useVisualState:Wt({scrapeMotionValuesFromProps:ut,createRenderState:$})};function $e(e,t,r,i={passive:!0}){return e.addEventListener(t,r,i),()=>e.removeEventListener(t,r)}let Vt=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function et(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}let Jt=e=>t=>Vt(t)&&e(t,et(t));function Ae(e,t,r,i){return $e(e,t,Jt(r),i)}let Wn=(e,t)=>r=>t(e(r)),dt=(...e)=>e.reduce(Wn);function Gr(e){let t=null;return()=>t===null&&(t=e,()=>{t=null})}let Hr=Gr("dragHorizontal"),Yr=Gr("dragVertical");function Xr(e){let t=!1;if(e==="y")t=Yr();else if(e==="x")t=Hr();else{let r=Hr(),i=Yr();r&&i?t=()=>{r(),i()}:(r&&r(),i&&i())}return t}function qr(){let e=Xr(!0);return!e||(e(),!1)}class ct{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Kr(e,t){let r="onHover"+(t?"Start":"End");return Ae(e.current,"pointer"+(t?"enter":"leave"),(i,a)=>{if(i.pointerType==="touch"||qr())return;let n=e.getProps();e.animationState&&n.whileHover&&e.animationState.setActive("whileHover",t),n[r]&&D.Wi.update(()=>n[r](i,a))},{passive:!e.getProps()[r]})}class zn extends ct{mount(){this.unmount=dt(Kr(this.node,!0),Kr(this.node,!1))}unmount(){}}class Un extends ct{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=dt($e(this.node.current,"focus",()=>this.onFocus()),$e(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let Jr=(e,t)=>!!t&&(e===t||Jr(e,t.parentElement));var Ee=k(4552);function cr(e,t){if(!t)return;let r=new PointerEvent("pointer"+e);t(r,et(r))}class _n extends ct{constructor(){super(...arguments),this.removeStartListeners=Ee.Z,this.removeEndListeners=Ee.Z,this.removeAccessibleListeners=Ee.Z,this.startPointerPress=(t,r)=>{if(this.isPressing)return;this.removeEndListeners();let i=this.node.getProps(),a=Ae(window,"pointerup",(o,s)=>{if(!this.checkPressEnd())return;let{onTap:l,onTapCancel:u,globalTapTarget:d}=this.node.getProps();D.Wi.update(()=>{d||Jr(this.node.current,o.target)?l&&l(o,s):u&&u(o,s)})},{passive:!(i.onTap||i.onPointerUp)}),n=Ae(window,"pointercancel",(o,s)=>this.cancelPress(o,s),{passive:!(i.onTapCancel||i.onPointerCancel)});this.removeEndListeners=dt(a,n),this.startPress(t,r)},this.startAccessiblePress=()=>{let t=$e(this.node.current,"keydown",i=>{i.key!=="Enter"||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=$e(this.node.current,"keyup",a=>{a.key==="Enter"&&this.checkPressEnd()&&cr("up",(n,o)=>{let{onTap:s}=this.node.getProps();s&&D.Wi.update(()=>s(n,o))})}),cr("down",(a,n)=>{this.startPress(a,n)}))}),r=$e(this.node.current,"blur",()=>{this.isPressing&&cr("cancel",(i,a)=>this.cancelPress(i,a))});this.removeAccessibleListeners=dt(t,r)}}startPress(t,r){this.isPressing=!0;let{onTapStart:i,whileTap:a}=this.node.getProps();a&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),i&&D.Wi.update(()=>i(t,r))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!qr()}cancelPress(t,r){if(!this.checkPressEnd())return;let{onTapCancel:i}=this.node.getProps();i&&D.Wi.update(()=>i(t,r))}mount(){let t=this.node.getProps(),r=Ae(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),i=$e(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=dt(r,i)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let pr=new WeakMap,mr=new WeakMap,$n=e=>{let t=pr.get(e.target);t&&t(e)},Zn=e=>{e.forEach($n)},Gn={some:0,all:1};class Hn extends ct{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:r,margin:i,amount:a="some",once:n}=t,o={root:r?r.current:void 0,rootMargin:i,threshold:typeof a=="number"?a:Gn[a]};return function(s,l,u){let d=function({root:h,...p}){let c=h||document;mr.has(c)||mr.set(c,{});let f=mr.get(c),v=JSON.stringify(p);return f[v]||(f[v]=new IntersectionObserver(Zn,{root:h,...p})),f[v]}(l);return pr.set(s,u),d.observe(s),()=>{pr.delete(s),d.unobserve(s)}}(this.node.current,o,s=>{let{isIntersecting:l}=s;if(this.isInView===l||(this.isInView=l,n&&!l&&this.hasEnteredView))return;l&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",l);let{onViewportEnter:u,onViewportLeave:d}=this.node.getProps(),h=l?u:d;h&&h(s)})}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;let{props:t,prevProps:r}=this.node;["amount","margin","root"].some(function({viewport:i={}},{viewport:a={}}={}){return n=>i[n]!==a[n]}(t,r))&&this.startObserver()}unmount(){}}function Qr(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let i=0;i<r;i++)if(t[i]!==e[i])return!1;return!0}function Qt(e,t,r){let i=e.getProps();return At(i,t,r!==void 0?r:i.custom,function(a){let n={};return a.values.forEach((o,s)=>n[s]=o.get()),n}(e),function(a){let n={};return a.values.forEach((o,s)=>n[s]=o.getVelocity()),n}(e))}var Ye=k(114);let pt=e=>1e3*e,ot=e=>e/1e3,ei={current:!1},ti=e=>Array.isArray(e)&&typeof e[0]=="number",zt=([e,t,r,i])=>`cubic-bezier(${e}, ${t}, ${r}, ${i})`,ri={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:zt([0,.65,.55,1]),circOut:zt([.55,0,1,.45]),backIn:zt([.31,.01,.66,-.59]),backOut:zt([.33,1.53,.69,.99])},ii=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function Ut(e,t,r,i){if(e===t&&r===i)return Ee.Z;let a=n=>function(o,s,l,u,d){let h,p,c=0;do(h=ii(p=s+(l-s)/2,u,d)-o)>0?l=p:s=p;while(Math.abs(h)>1e-7&&++c<12);return p}(n,0,1,e,r);return n=>n===0||n===1?n:ii(a(n),t,i)}let Yn=Ut(.42,0,1,1),Xn=Ut(0,0,.58,1),ni=Ut(.42,0,.58,1),qn=e=>Array.isArray(e)&&typeof e[0]!="number",oi=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,si=e=>t=>1-e(1-t),fr=e=>1-Math.sin(Math.acos(e)),ai=si(fr),Kn=oi(fr),li=Ut(.33,1.53,.69,.99),gr=si(li),Jn=oi(gr),ui={linear:Ee.Z,easeIn:Yn,easeInOut:ni,easeOut:Xn,circIn:fr,circInOut:Kn,circOut:ai,backIn:gr,backInOut:Jn,backOut:li,anticipate:e=>(e*=2)<1?.5*gr(e):.5*(2-Math.pow(2,-10*(e-1)))},hi=e=>{if(Array.isArray(e)){(0,Ye.k)(e.length===4,"Cubic bezier arrays must contain four numerical values.");let[t,r,i,a]=e;return Ut(t,r,i,a)}return typeof e=="string"?((0,Ye.k)(ui[e]!==void 0,`Invalid easing type '${e}'`),ui[e]):e},yr=(e,t)=>r=>!!(at(r)&&dr.test(r)&&r.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(r,t)),di=(e,t,r)=>i=>{if(!at(i))return i;let[a,n,o,s]=i.match(Ke);return{[e]:parseFloat(a),[t]:parseFloat(n),[r]:parseFloat(o),alpha:s!==void 0?parseFloat(s):1}},Qn=e=>qe(0,255,e),vr={...it,transform:e=>Math.round(Qn(e))},xt={test:yr("rgb","red"),parse:di("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:i=1})=>"rgba("+vr.transform(e)+", "+vr.transform(t)+", "+vr.transform(r)+", "+vt(yt.transform(i))+")"},br={test:yr("#"),parse:function(e){let t="",r="",i="",a="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),i=e.substring(5,7),a=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),i=e.substring(3,4),a=e.substring(4,5),t+=t,r+=r,i+=i,a+=a),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:a?parseInt(a,16)/255:1}},transform:xt.transform},Mt={test:yr("hsl","hue"),parse:di("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:i=1})=>"hsla("+Math.round(e)+", "+Ue.transform(vt(t))+", "+Ue.transform(vt(r))+", "+vt(yt.transform(i))+")"},Fe={test:e=>xt.test(e)||br.test(e)||Mt.test(e),parse:e=>xt.test(e)?xt.parse(e):Mt.test(e)?Mt.parse(e):br.parse(e),transform:e=>at(e)?e:e.hasOwnProperty("red")?xt.transform(e):Mt.transform(e)},me=(e,t,r)=>-r*e+r*t+e;function xr(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<.16666666666666666?e+(t-e)*6*r:r<.5?t:r<.6666666666666666?e+(t-e)*(.6666666666666666-r)*6:e}let wr=(e,t,r)=>{let i=e*e;return Math.sqrt(Math.max(0,r*(t*t-i)+i))},eo=[br,xt,Mt],to=e=>eo.find(t=>t.test(e));function ci(e){let t=to(e);(0,Ye.k)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`);let r=t.parse(e);return t===Mt&&(r=function({hue:i,saturation:a,lightness:n,alpha:o}){i/=360,n/=100;let s=0,l=0,u=0;if(a/=100){let d=n<.5?n*(1+a):n+a-n*a,h=2*n-d;s=xr(h,d,i+.3333333333333333),l=xr(h,d,i),u=xr(h,d,i-.3333333333333333)}else s=l=u=n;return{red:Math.round(255*s),green:Math.round(255*l),blue:Math.round(255*u),alpha:o}}(r)),r}let pi=(e,t)=>{let r=ci(e),i=ci(t),a={...r};return n=>(a.red=wr(r.red,i.red,n),a.green=wr(r.green,i.green,n),a.blue=wr(r.blue,i.blue,n),a.alpha=me(r.alpha,i.alpha,n),xt.transform(a))},mi={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:Ee.Z},fi={regex:nt,countKey:"Colors",token:"${c}",parse:Fe.parse},gi={regex:Ke,countKey:"Numbers",token:"${n}",parse:it.parse};function Pr(e,{regex:t,countKey:r,token:i,parse:a}){let n=e.tokenised.match(t);n&&(e["num"+r]=n.length,e.tokenised=e.tokenised.replace(t,i),e.values.push(...n.map(a)))}function er(e){let t=e.toString(),r={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&Pr(r,mi),Pr(r,fi),Pr(r,gi),r}function yi(e){return er(e).values}function vi(e){let{values:t,numColors:r,numVars:i,tokenised:a}=er(e),n=t.length;return o=>{let s=a;for(let l=0;l<n;l++)s=l<i?s.replace(mi.token,o[l]):l<i+r?s.replace(fi.token,Fe.transform(o[l])):s.replace(gi.token,vt(o[l]));return s}}let ro=e=>typeof e=="number"?0:e,mt={test:function(e){var t,r;return isNaN(e)&&at(e)&&(((t=e.match(Ke))===null||t===void 0?void 0:t.length)||0)+(((r=e.match(nt))===null||r===void 0?void 0:r.length)||0)>0},parse:yi,createTransformer:vi,getAnimatableNone:function(e){let t=yi(e);return vi(e)(t.map(ro))}},bi=(e,t)=>r=>`${r>0?t:e}`;function xi(e,t){return typeof e=="number"?r=>me(e,t,r):Fe.test(e)?pi(e,t):e.startsWith("var(")?bi(e,t):Pi(e,t)}let wi=(e,t)=>{let r=[...e],i=r.length,a=e.map((n,o)=>xi(n,t[o]));return n=>{for(let o=0;o<i;o++)r[o]=a[o](n);return r}},io=(e,t)=>{let r={...e,...t},i={};for(let a in r)e[a]!==void 0&&t[a]!==void 0&&(i[a]=xi(e[a],t[a]));return a=>{for(let n in i)r[n]=i[n](a);return r}},Pi=(e,t)=>{let r=mt.createTransformer(t),i=er(e),a=er(t);return i.numVars===a.numVars&&i.numColors===a.numColors&&i.numNumbers>=a.numNumbers?dt(wi(i.values,a.values),r):((0,Ye.K)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),bi(e,t))},_t=(e,t,r)=>{let i=t-e;return i===0?1:(r-e)/i},no=(e,t)=>r=>me(e,t,r);function Si(e,t,{clamp:r=!0,ease:i,mixer:a}={}){let n=e.length;if((0,Ye.k)(n===t.length,"Both input and output ranges must be the same length"),n===1)return()=>t[0];e[0]>e[n-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(u,d,h){let p=[],c=h||function(v){if(typeof v!="number"){if(typeof v=="string")return Fe.test(v)?pi:Pi;if(Array.isArray(v))return wi;if(typeof v=="object")return io}return no}(u[0]),f=u.length-1;for(let v=0;v<f;v++){let g=c(u[v],u[v+1]);d&&(g=dt(Array.isArray(d)?d[v]||Ee.Z:d,g)),p.push(g)}return p}(t,i,a),s=o.length,l=u=>{let d=0;if(s>1)for(;d<e.length-2&&!(u<e[d+1]);d++);let h=_t(e[d],e[d+1],u);return o[d](h)};return r?u=>l(qe(e[0],e[n-1],u)):l}function tr({duration:e=300,keyframes:t,times:r,ease:i="easeInOut"}){let a=qn(i)?i.map(hi):hi(i),n={done:!1,value:t[0]},o=Si((r&&r.length===t.length?r:function(s){let l=[0];return function(u,d){let h=u[u.length-1];for(let p=1;p<=d;p++){let c=_t(0,d,p);u.push(me(h,1,c))}}(l,s.length-1),l}(t)).map(s=>s*e),t,{ease:Array.isArray(a)?a:t.map(()=>a||ni).splice(0,t.length-1)});return{calculatedDuration:e,next:s=>(n.value=o(s),n.done=s>=e,n)}}function Ti(e,t,r){var i,a;let n=Math.max(t-5,0);return i=r-e(n),(a=t-n)?1e3/a*i:0}function Sr(e,t){return e*Math.sqrt(1-t*t)}let oo=["duration","bounce"],so=["stiffness","damping","mass"];function ki(e,t){return t.some(r=>e[r]!==void 0)}function Ci({keyframes:e,restDelta:t,restSpeed:r,...i}){let a,n=e[0],o=e[e.length-1],s={done:!1,value:n},{stiffness:l,damping:u,mass:d,duration:h,velocity:p,isResolvedFromDuration:c}=function(y){let x={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...y};if(!ki(y,so)&&ki(y,oo)){let T=function({duration:C=800,bounce:E=.25,velocity:S=0,mass:F=1}){let Q,he;(0,Ye.K)(C<=pt(10),"Spring duration must be 10 seconds or less");let ie=1-E;ie=qe(.05,1,ie),C=qe(.01,10,ot(C)),ie<1?(Q=M=>{let H=M*ie,R=H*C;return .001-(H-S)/Sr(M,ie)*Math.exp(-R)},he=M=>{let H=M*ie*C,R=Math.pow(ie,2)*Math.pow(M,2)*C,ae=Sr(Math.pow(M,2),ie);return(H*S+S-R)*Math.exp(-H)*(-Q(M)+.001>0?-1:1)/ae}):(Q=M=>-.001+Math.exp(-M*C)*((M-S)*C+1),he=M=>C*C*(S-M)*Math.exp(-M*C));let _=function(M,H,R){let ae=R;for(let Be=1;Be<12;Be++)ae-=M(ae)/H(ae);return ae}(Q,he,5/C);if(C=pt(C),isNaN(_))return{stiffness:100,damping:10,duration:C};{let M=Math.pow(_,2)*F;return{stiffness:M,damping:2*ie*Math.sqrt(F*M),duration:C}}}(y);(x={...x,...T,mass:1}).isResolvedFromDuration=!0}return x}({...i,velocity:-ot(i.velocity||0)}),f=p||0,v=u/(2*Math.sqrt(l*d)),g=o-n,w=ot(Math.sqrt(l/d)),b=5>Math.abs(g);if(r||(r=b?.01:2),t||(t=b?.005:.5),v<1){let y=Sr(w,v);a=x=>o-Math.exp(-v*w*x)*((f+v*w*g)/y*Math.sin(y*x)+g*Math.cos(y*x))}else if(v===1)a=y=>o-Math.exp(-w*y)*(g+(f+w*g)*y);else{let y=w*Math.sqrt(v*v-1);a=x=>{let T=Math.exp(-v*w*x),C=Math.min(y*x,300);return o-T*((f+v*w*g)*Math.sinh(C)+y*g*Math.cosh(C))/y}}return{calculatedDuration:c&&h||null,next:y=>{let x=a(y);if(c)s.done=y>=h;else{let T=f;y!==0&&(T=v<1?Ti(a,y,x):0);let C=Math.abs(T)<=r,E=Math.abs(o-x)<=t;s.done=C&&E}return s.value=s.done?o:x,s}}}function Ai({keyframes:e,velocity:t=0,power:r=.8,timeConstant:i=325,bounceDamping:a=10,bounceStiffness:n=500,modifyTarget:o,min:s,max:l,restDelta:u=.5,restSpeed:d}){let h,p,c=e[0],f={done:!1,value:c},v=S=>s!==void 0&&S<s||l!==void 0&&S>l,g=S=>s===void 0?l:l===void 0||Math.abs(s-S)<Math.abs(l-S)?s:l,w=r*t,b=c+w,y=o===void 0?b:o(b);y!==b&&(w=y-c);let x=S=>-w*Math.exp(-S/i),T=S=>y+x(S),C=S=>{let F=x(S),Q=T(S);f.done=Math.abs(F)<=u,f.value=f.done?y:Q},E=S=>{v(f.value)&&(h=S,p=Ci({keyframes:[f.value,g(f.value)],velocity:Ti(T,S,f.value),damping:a,stiffness:n,restDelta:u,restSpeed:d}))};return E(0),{calculatedDuration:null,next:S=>{let F=!1;return p||h!==void 0||(F=!0,C(S),E(S)),h!==void 0&&S>h?p.next(S-h):(F||C(S),f)}}}let ao=e=>{let t=({timestamp:r})=>e(r);return{start:()=>D.Wi.update(t,!0),stop:()=>(0,D.Pn)(t),now:()=>D.frameData.isProcessing?D.frameData.timestamp:performance.now()}};function Ei(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}let lo={decay:Ai,inertia:Ai,tween:tr,keyframes:tr,spring:Ci};function rr({autoplay:e=!0,delay:t=0,driver:r=ao,keyframes:i,type:a="keyframes",repeat:n=0,repeatDelay:o=0,repeatType:s="loop",onPlay:l,onStop:u,onComplete:d,onUpdate:h,...p}){let c,f,v,g,w,b=1,y=!1,x=()=>{f=new Promise(ye=>{c=ye})};x();let T=lo[a]||tr;T!==tr&&typeof i[0]!="number"&&(g=Si([0,100],i,{clamp:!1}),i=[0,100]);let C=T({...p,keyframes:i});s==="mirror"&&(w=T({...p,keyframes:[...i].reverse(),velocity:-(p.velocity||0)}));let E="idle",S=null,F=null,Q=null;C.calculatedDuration===null&&n&&(C.calculatedDuration=Ei(C));let{calculatedDuration:he}=C,ie=1/0,_=1/0;he!==null&&(_=(ie=he+o)*(n+1)-o);let M=0,H=ye=>{if(F===null)return;b>0&&(F=Math.min(F,ye)),b<0&&(F=Math.min(ye-_/b,F));let Tt=(M=S!==null?S:Math.round(ye-F)*b)-t*(b>=0?1:-1),lr=b>=0?Tt<0:Tt>_;M=Math.max(Tt,0),E==="finished"&&S===null&&(M=_);let Yt=M,Ft=C;if(n){let Ur=Math.min(M,_)/ie,_r=Math.floor(Ur),kt=Ur%1;!kt&&Ur>=1&&(kt=1),kt===1&&_r--,(_r=Math.min(_r,n+1))%2&&(s==="reverse"?(kt=1-kt,o&&(kt-=o/ie)):s==="mirror"&&(Ft=w)),Yt=qe(0,1,kt)*ie}let tt=lr?{done:!1,value:i[0]}:Ft.next(Yt);g&&(tt.value=g(tt.value));let{done:ur}=tt;lr||he===null||(ur=b>=0?M>=_:M<=0);let gt=S===null&&(E==="finished"||E==="running"&&ur);return h&&h(tt.value),gt&&Be(),tt},R=()=>{v&&v.stop(),v=void 0},ae=()=>{E="idle",R(),c(),x(),F=Q=null},Be=()=>{E="finished",d&&d(),R(),c()},Xe=()=>{if(y)return;v||(v=r(H));let ye=v.now();l&&l(),S!==null?F=ye-S:F&&E!=="finished"||(F=ye),E==="finished"&&x(),Q=F,S=null,E="running",v.start()};e&&Xe();let ar={then:(ye,Tt)=>f.then(ye,Tt),get time(){return ot(M)},set time(ye){M=ye=pt(ye),S===null&&v&&b!==0?F=v.now()-ye/b:S=ye},get duration(){return ot(C.calculatedDuration===null?Ei(C):C.calculatedDuration)},get speed(){return b},set speed(ye){ye===b||!v||(b=ye,ar.time=ot(M))},get state(){return E},play:Xe,pause:()=>{E="paused",S=M},stop:()=>{y=!0,E!=="idle"&&(E="idle",u&&u(),ae())},cancel:()=>{Q!==null&&H(Q),ae()},complete:()=>{E="finished"},sample:ye=>(F=0,H(ye))};return ar}let uo=function(e){let t;return()=>(t===void 0&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ho=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),co=(e,t)=>t.type==="spring"||e==="backgroundColor"||!function r(i){return!!(!i||typeof i=="string"&&ri[i]||ti(i)||Array.isArray(i)&&i.every(r))}(t.ease),po={type:"spring",stiffness:500,damping:25,restSpeed:10},mo=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),fo={type:"keyframes",duration:.8},go={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},yo=(e,{keyframes:t})=>t.length>2?fo:re.has(e)?e.startsWith("scale")?mo(t[1]):po:go,Tr=(e,t)=>e!=="zIndex"&&!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(mt.test(t)||t==="0")&&!t.startsWith("url(")),vo=new Set(["brightness","contrast","saturate","opacity"]);function bo(e){let[t,r]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;let[i]=r.match(Ke)||[];if(!i)return e;let a=r.replace(i,""),n=vo.has(t)?1:0;return i!==r&&(n*=100),t+"("+n+a+")"}let xo=/([a-z-]*)\(.*?\)/g,kr={...mt,getAnimatableNone:e=>{let t=e.match(xo);return t?t.map(bo).join(" "):e}},wo={...L,color:Fe,backgroundColor:Fe,outlineColor:Fe,fill:Fe,stroke:Fe,borderColor:Fe,borderTopColor:Fe,borderRightColor:Fe,borderBottomColor:Fe,borderLeftColor:Fe,filter:kr,WebkitFilter:kr},Cr=e=>wo[e];function Vi(e,t){let r=Cr(e);return r!==kr&&(r=mt),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let Mi=e=>/^0[^.\s]+$/.test(e);function Ar(e,t){return e[t]||e.default||e}let Po={skipAnimations:!1},Er=(e,t,r,i={})=>a=>{let n=Ar(i,e)||{},o=n.delay||i.delay||0,{elapsed:s=0}=i;s-=pt(o);let l=function(f,v,g,w){let b,y,x=Tr(v,g);b=Array.isArray(g)?[...g]:[null,g];let T=w.from!==void 0?w.from:f.get(),C=[];for(let S=0;S<b.length;S++){var E;b[S]===null&&(b[S]=S===0?T:b[S-1]),(typeof(E=b[S])=="number"?E===0:E!==null?E==="none"||E==="0"||Mi(E):void 0)&&C.push(S),typeof b[S]=="string"&&b[S]!=="none"&&b[S]!=="0"&&(y=b[S])}if(x&&C.length&&y)for(let S=0;S<C.length;S++)b[C[S]]=Vi(v,y);return b}(t,e,r,n),u=l[0],d=l[l.length-1],h=Tr(e,u),p=Tr(e,d);(0,Ye.K)(h===p,`You are trying to animate ${e} from "${u}" to "${d}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${d} via the \`style\` property.`);let c={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...n,delay:-s,onUpdate:f=>{t.set(f),n.onUpdate&&n.onUpdate(f)},onComplete:()=>{a(),n.onComplete&&n.onComplete()}};if(!function({when:f,delay:v,delayChildren:g,staggerChildren:w,staggerDirection:b,repeat:y,repeatType:x,repeatDelay:T,from:C,elapsed:E,...S}){return!!Object.keys(S).length}(n)&&(c={...c,...yo(e,c)}),c.duration&&(c.duration=pt(c.duration)),c.repeatDelay&&(c.repeatDelay=pt(c.repeatDelay)),!h||!p||ei.current||n.type===!1||Po.skipAnimations)return function({keyframes:f,delay:v,onUpdate:g,onComplete:w}){let b=()=>(g&&g(f[f.length-1]),w&&w(),{time:0,speed:1,duration:0,play:Ee.Z,pause:Ee.Z,stop:Ee.Z,then:y=>(y(),Promise.resolve()),cancel:Ee.Z,complete:Ee.Z});return v?rr({keyframes:[0,1],duration:0,delay:v,onComplete:b}):b()}(ei.current?{...c,delay:0}:c);if(!i.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let f=function(v,g,{onUpdate:w,onComplete:b,...y}){let x,T;if(!(uo()&&ho.has(g)&&!y.repeatDelay&&y.repeatType!=="mirror"&&y.damping!==0&&y.type!=="inertia"))return!1;let C=!1,E=!1,S=()=>{T=new Promise(R=>{x=R})};S();let{keyframes:F,duration:Q=300,ease:he,times:ie}=y;if(co(g,y)){let R=rr({...y,repeat:0,delay:0}),ae={done:!1,value:F[0]},Be=[],Xe=0;for(;!ae.done&&Xe<2e4;)ae=R.sample(Xe),Be.push(ae.value),Xe+=10;ie=void 0,F=Be,Q=Xe-10,he="linear"}let _=function(R,ae,Be,{delay:Xe=0,duration:ar,repeat:ye=0,repeatType:Tt="loop",ease:lr,times:Yt}={}){let Ft={[ae]:Be};Yt&&(Ft.offset=Yt);let tt=function ur(gt){if(gt)return ti(gt)?zt(gt):Array.isArray(gt)?gt.map(ur):ri[gt]}(lr);return Array.isArray(tt)&&(Ft.easing=tt),R.animate(Ft,{delay:Xe,duration:ar,easing:Array.isArray(tt)?"linear":tt,fill:"both",iterations:ye+1,direction:Tt==="reverse"?"alternate":"normal"})}(v.owner.current,g,F,{...y,duration:Q,ease:he,times:ie}),M=()=>{E=!1,_.cancel()},H=()=>{E=!0,D.Wi.update(M),x(),S()};return _.onfinish=()=>{E||(v.set(function(R,{repeat:ae,repeatType:Be="loop"}){let Xe=ae&&Be!=="loop"&&ae%2==1?0:R.length-1;return R[Xe]}(F,y)),b&&b(),H())},{then:(R,ae)=>T.then(R,ae),attachTimeline:R=>(_.timeline=R,_.onfinish=null,Ee.Z),get time(){return ot(_.currentTime||0)},set time(R){_.currentTime=pt(R)},get speed(){return _.playbackRate},set speed(R){_.playbackRate=R},get duration(){return ot(Q)},play:()=>{C||(_.play(),(0,D.Pn)(M))},pause:()=>_.pause(),stop:()=>{if(C=!0,_.playState==="idle")return;let{currentTime:R}=_;if(R){let ae=rr({...y,autoplay:!1});v.setWithVelocity(ae.sample(R-10).value,ae.sample(R).value,10)}H()},complete:()=>{E||_.finish()},cancel:H}}(t,e,c);if(f)return f}return rr(c)};function ir(e){return!!(le(e)&&e.add)}let Di=e=>/^\-?\d*\.?\d+$/.test(e);function Vr(e,t){e.indexOf(t)===-1&&e.push(t)}function Mr(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class Dr{constructor(){this.subscriptions=[]}add(t){return Vr(this.subscriptions,t),()=>Mr(this.subscriptions,t)}notify(t,r,i){let a=this.subscriptions.length;if(a)if(a===1)this.subscriptions[0](t,r,i);else for(let n=0;n<a;n++){let o=this.subscriptions[n];o&&o(t,r,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let So=e=>!isNaN(parseFloat(e)),ji={current:void 0};class To{constructor(t,r={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(i,a=!0)=>{this.prev=this.current,this.current=i;let{delta:n,timestamp:o}=D.frameData;this.lastUpdated!==o&&(this.timeDelta=n,this.lastUpdated=o,D.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),a&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>D.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:i})=>{i!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=So(this.current),this.owner=r.owner}onChange(t){return this.on("change",t)}on(t,r){this.events[t]||(this.events[t]=new Dr);let i=this.events[t].add(r);return t==="change"?()=>{i(),D.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,r){this.passiveEffect=t,this.stopPassiveEffect=r}set(t,r=!0){r&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,r)}setWithVelocity(t,r,i){this.set(r),this.prev=t,this.timeDelta=i}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return ji.current&&ji.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t,r;return this.canTrackVelocity?(t=parseFloat(this.current)-parseFloat(this.prev),(r=this.timeDelta)?1e3/r*t:0):0}start(t){return this.stop(),new Promise(r=>{this.hasAnimated=!0,this.animation=t(r),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Dt(e,t){return new To(e,t)}let Ri=e=>t=>t.test(e),Li=[it,m,Ue,Je,V,A,{test:e=>e==="auto",parse:e=>e}],$t=e=>Li.find(Ri(e)),ko=[...Li,Fe,mt],Co=e=>ko.find(Ri(e));function Fi(e,t,{delay:r=0,transitionOverride:i,type:a}={}){let{transition:n=e.getDefaultTransition(),transitionEnd:o,...s}=e.makeTargetAnimatable(t),l=e.getValue("willChange");i&&(n=i);let u=[],d=a&&e.animationState&&e.animationState.getState()[a];for(let h in s){let p=e.getValue(h),c=s[h];if(!p||c===void 0||d&&function({protectedKeys:w,needsAnimating:b},y){let x=w.hasOwnProperty(y)&&b[y]!==!0;return b[y]=!1,x}(d,h))continue;let f={delay:r,elapsed:0,...Ar(n||{},h)};if(window.HandoffAppearAnimations){let w=e.getProps()[W];if(w){let b=window.HandoffAppearAnimations(w,h,p,D.Wi);b!==null&&(f.elapsed=b,f.isHandoff=!0)}}let v=!f.isHandoff&&!function(w,b){let y=w.get();if(!Array.isArray(b))return y!==b;for(let x=0;x<b.length;x++)if(b[x]!==y)return!0}(p,c);if(f.type==="spring"&&(p.getVelocity()||f.velocity)&&(v=!1),p.animation&&(v=!1),v)continue;p.start(Er(h,p,c,e.shouldReduceMotion&&re.has(h)?{type:!1}:f));let g=p.animation;ir(l)&&(l.add(h),g.then(()=>l.remove(h))),u.push(g)}return o&&Promise.all(u).then(()=>{o&&function(h,p){let c=Qt(h,p),{transitionEnd:f={},transition:v={},...g}=c?h.makeTargetAnimatable(c,!1):{};for(let w in g={...g,...f}){let b=It(g[w]);h.hasValue(w)?h.getValue(w).set(b):h.addValue(w,Dt(b))}}(e,o)}),u}function jr(e,t,r={}){let i=Qt(e,t,r.custom),{transition:a=e.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(a=r.transitionOverride);let n=i?()=>Promise.all(Fi(e,i,r)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(l=0)=>{let{delayChildren:u=0,staggerChildren:d,staggerDirection:h}=a;return function(p,c,f=0,v=0,g=1,w){let b=[],y=(p.variantChildren.size-1)*v,x=g===1?(T=0)=>T*v:(T=0)=>y-T*v;return Array.from(p.variantChildren).sort(Ao).forEach((T,C)=>{T.notify("AnimationStart",c),b.push(jr(T,c,{...w,delay:f+x(C)}).then(()=>T.notify("AnimationComplete",c)))}),Promise.all(b)}(e,t,u+l,d,h,r)}:()=>Promise.resolve(),{when:s}=a;if(!s)return Promise.all([n(),o(r.delay)]);{let[l,u]=s==="beforeChildren"?[n,o]:[o,n];return l().then(()=>u())}}function Ao(e,t){return e.sortNodePosition(t)}let Eo=[...J].reverse(),Vo=J.length;function wt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class Mo extends ct{constructor(t){super(t),t.animationState||(t.animationState=function(r){let i=l=>Promise.all(l.map(({animation:u,options:d})=>function(h,p,c={}){let f;if(h.notify("AnimationStart",p),Array.isArray(p))f=Promise.all(p.map(v=>jr(h,v,c)));else if(typeof p=="string")f=jr(h,p,c);else{let v=typeof p=="function"?Qt(h,p,c.custom):p;f=Promise.all(Fi(h,v,c))}return f.then(()=>h.notify("AnimationComplete",p))}(r,u,d))),a={animate:wt(!0),whileInView:wt(),whileHover:wt(),whileTap:wt(),whileDrag:wt(),whileFocus:wt(),exit:wt()},n=!0,o=(l,u)=>{let d=Qt(r,u);if(d){let{transition:h,transitionEnd:p,...c}=d;l={...l,...c,...p}}return l};function s(l,u){let d=r.getProps(),h=r.getVariantContext(!0)||{},p=[],c=new Set,f={},v=1/0;for(let b=0;b<Vo;b++){var g;let y=Eo[b],x=a[y],T=d[y]!==void 0?d[y]:h[y],C=N(T),E=y===u?x.isActive:null;E===!1&&(v=b);let S=T===h[y]&&T!==d[y]&&C;if(S&&n&&r.manuallyAnimateOnMount&&(S=!1),x.protectedKeys={...f},!x.isActive&&E===null||!T&&!x.prevProp||U(T)||typeof T=="boolean")continue;let F=(g=x.prevProp,(typeof T=="string"?T!==g:!!Array.isArray(T)&&!Qr(T,g))||y===u&&x.isActive&&!S&&C||b>v&&C),Q=!1,he=Array.isArray(T)?T:[T],ie=he.reduce(o,{});E===!1&&(ie={});let{prevResolvedValues:_={}}=x,M={..._,...ie},H=R=>{F=!0,c.has(R)&&(Q=!0,c.delete(R)),x.needsAnimating[R]=!0};for(let R in M){let ae=ie[R],Be=_[R];f.hasOwnProperty(R)||((ht(ae)&&ht(Be)?Qr(ae,Be):ae===Be)?ae!==void 0&&c.has(R)?H(R):x.protectedKeys[R]=!0:ae!==void 0?H(R):c.add(R))}x.prevProp=T,x.prevResolvedValues=ie,x.isActive&&(f={...f,...ie}),n&&r.blockInitialAnimation&&(F=!1),F&&(!S||Q)&&p.push(...he.map(R=>({animation:R,options:{type:y,...l}})))}if(c.size){let b={};c.forEach(y=>{let x=r.getBaseTarget(y);x!==void 0&&(b[y]=x)}),p.push({animation:b})}let w=!!p.length;return n&&(d.initial===!1||d.initial===d.animate)&&!r.manuallyAnimateOnMount&&(w=!1),n=!1,w?i(p):Promise.resolve()}return{animateChanges:s,setActive:function(l,u,d){var h;if(a[l].isActive===u)return Promise.resolve();(h=r.variantChildren)===null||h===void 0||h.forEach(c=>{var f;return(f=c.animationState)===null||f===void 0?void 0:f.setActive(l,u)}),a[l].isActive=u;let p=s(d,l);for(let c in a)a[c].protectedKeys={};return p},setAnimateFunction:function(l){i=l(r)},getState:()=>a}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),U(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:r}=this.node.prevProps||{};t!==r&&this.updateAnimationControlsSubscription()}unmount(){}}let Do=0;class jo extends ct{constructor(){super(...arguments),this.id=Do++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:r,custom:i}=this.node.presenceContext,{isPresent:a}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===a)return;let n=this.node.animationState.setActive("exit",!t,{custom:i??this.node.getProps().custom});r&&!t&&n.then(()=>r(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let Bi=(e,t)=>Math.abs(e-t);class Oi{constructor(t,r,{transformPagePoint:i,contextWindow:a,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let d=Lr(this.lastMoveEventInfo,this.history),h=this.startEvent!==null,p=function(w,b){return Math.sqrt(Bi(w.x,b.x)**2+Bi(w.y,b.y)**2)}(d.offset,{x:0,y:0})>=3;if(!h&&!p)return;let{point:c}=d,{timestamp:f}=D.frameData;this.history.push({...c,timestamp:f});let{onStart:v,onMove:g}=this.handlers;h||(v&&v(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,d)},this.handlePointerMove=(d,h)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=Rr(h,this.transformPagePoint),D.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(d,h)=>{this.end();let{onEnd:p,onSessionEnd:c,resumeAnimation:f}=this.handlers;if(this.dragSnapToOrigin&&f&&f(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let v=Lr(d.type==="pointercancel"?this.lastMoveEventInfo:Rr(h,this.transformPagePoint),this.history);this.startEvent&&p&&p(d,v),c&&c(d,v)},!Vt(t))return;this.dragSnapToOrigin=n,this.handlers=r,this.transformPagePoint=i,this.contextWindow=a||window;let o=Rr(et(t),this.transformPagePoint),{point:s}=o,{timestamp:l}=D.frameData;this.history=[{...s,timestamp:l}];let{onSessionStart:u}=r;u&&u(t,Lr(o,this.history)),this.removeListeners=dt(Ae(this.contextWindow,"pointermove",this.handlePointerMove),Ae(this.contextWindow,"pointerup",this.handlePointerUp),Ae(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,D.Pn)(this.updatePoint)}}function Rr(e,t){return t?{point:t(e.point)}:e}function Ni(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Lr({point:e},t){return{point:e,delta:Ni(e,Ii(t)),offset:Ni(e,t[0]),velocity:function(r,i){if(r.length<2)return{x:0,y:0};let a=r.length-1,n=null,o=Ii(r);for(;a>=0&&(n=r[a],!(o.timestamp-n.timestamp>pt(.1)));)a--;if(!n)return{x:0,y:0};let s=ot(o.timestamp-n.timestamp);if(s===0)return{x:0,y:0};let l={x:(o.x-n.x)/s,y:(o.y-n.y)/s};return l.x===1/0&&(l.x=0),l.y===1/0&&(l.y=0),l}(t,0)}}function Ii(e){return e[e.length-1]}function We(e){return e.max-e.min}function Fr(e,t=0,r=.01){return Math.abs(e-t)<=r}function Wi(e,t,r,i=.5){e.origin=i,e.originPoint=me(t.min,t.max,e.origin),e.scale=We(r)/We(t),(Fr(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=me(r.min,r.max,e.origin)-e.originPoint,(Fr(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Zt(e,t,r,i){Wi(e.x,t.x,r.x,i?i.originX:void 0),Wi(e.y,t.y,r.y,i?i.originY:void 0)}function zi(e,t,r){e.min=r.min+t.min,e.max=e.min+We(t)}function Ui(e,t,r){e.min=t.min-r.min,e.max=e.min+We(t)}function Gt(e,t,r){Ui(e.x,t.x,r.x),Ui(e.y,t.y,r.y)}function _i(e,t,r){return{min:t!==void 0?e.min+t:void 0,max:r!==void 0?e.max+r-(e.max-e.min):void 0}}function $i(e,t){let r=t.min-e.min,i=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,i]=[i,r]),{min:r,max:i}}function Zi(e,t,r){return{min:Gi(e,t),max:Gi(e,r)}}function Gi(e,t){return typeof e=="number"?e:e[t]||0}let Hi=()=>({translate:0,scale:1,origin:0,originPoint:0}),jt=()=>({x:Hi(),y:Hi()}),Yi=()=>({min:0,max:0}),we=()=>({x:Yi(),y:Yi()});function Ze(e){return[e("x"),e("y")]}function Xi({top:e,left:t,right:r,bottom:i}){return{x:{min:t,max:r},y:{min:e,max:i}}}function Br(e){return e===void 0||e===1}function Or({scale:e,scaleX:t,scaleY:r}){return!Br(e)||!Br(t)||!Br(r)}function Pt(e){return Or(e)||qi(e)||e.z||e.rotate||e.rotateX||e.rotateY}function qi(e){var t,r;return(t=e.x)&&t!=="0%"||(r=e.y)&&r!=="0%"}function Ki(e,t,r,i,a){return a!==void 0&&(e=i+a*(e-i)),i+r*(e-i)+t}function Nr(e,t=0,r=1,i,a){e.min=Ki(e.min,t,r,i,a),e.max=Ki(e.max,t,r,i,a)}function Ji(e,{x:t,y:r}){Nr(e.x,t.translate,t.scale,t.originPoint),Nr(e.y,r.translate,r.scale,r.originPoint)}function Qi(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function ft(e,t){e.min=e.min+t,e.max=e.max+t}function en(e,t,[r,i,a]){let n=t[a]!==void 0?t[a]:.5,o=me(e.min,e.max,n);Nr(e,t[r],t[i],o,t.scale)}let Ro=["x","scaleX","originX"],Lo=["y","scaleY","originY"];function Rt(e,t){en(e.x,t,Ro),en(e.y,t,Lo)}function tn(e,t){return Xi(function(r,i){if(!i)return r;let a=i({x:r.left,y:r.top}),n=i({x:r.right,y:r.bottom});return{top:a.y,left:a.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let rn=({current:e})=>e?e.ownerDocument.defaultView:null,Fo=new WeakMap;class Bo{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=we(),this.visualElement=t}start(t,{snapToCursor:r=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&i.isPresent===!1)return;let{dragSnapToOrigin:a}=this.getProps();this.panSession=new Oi(t,{onSessionStart:n=>{let{dragSnapToOrigin:o}=this.getProps();o?this.pauseAnimation():this.stopAnimation(),r&&this.snapToCursor(et(n,"page").point)},onStart:(n,o)=>{let{drag:s,dragPropagation:l,onDragStart:u}=this.getProps();if(s&&!l&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Xr(s),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ze(h=>{let p=this.getAxisMotionValue(h).get()||0;if(Ue.test(p)){let{projection:c}=this.visualElement;if(c&&c.layout){let f=c.layout.layoutBox[h];if(f){let v=We(f);p=parseFloat(p)/100*v}}}this.originPoint[h]=p}),u&&D.Wi.update(()=>u(n,o),!1,!0);let{animationState:d}=this.visualElement;d&&d.setActive("whileDrag",!0)},onMove:(n,o)=>{let{dragPropagation:s,dragDirectionLock:l,onDirectionLock:u,onDrag:d}=this.getProps();if(!s&&!this.openGlobalLock)return;let{offset:h}=o;if(l&&this.currentDirection===null){this.currentDirection=function(p,c=10){let f=null;return Math.abs(p.y)>c?f="y":Math.abs(p.x)>c&&(f="x"),f}(h),this.currentDirection!==null&&u&&u(this.currentDirection);return}this.updateAxis("x",o.point,h),this.updateAxis("y",o.point,h),this.visualElement.render(),d&&d(n,o)},onSessionEnd:(n,o)=>this.stop(n,o),resumeAnimation:()=>Ze(n=>{var o;return this.getAnimationState(n)==="paused"&&((o=this.getAxisMotionValue(n).animation)===null||o===void 0?void 0:o.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:a,contextWindow:rn(this.visualElement)})}stop(t,r){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:a}=r;this.startAnimation(a);let{onDragEnd:n}=this.getProps();n&&D.Wi.update(()=>n(t,r))}cancel(){this.isDragging=!1;let{projection:t,animationState:r}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),r&&r.setActive("whileDrag",!1)}updateAxis(t,r,i){let{drag:a}=this.getProps();if(!i||!nr(t,a,this.currentDirection))return;let n=this.getAxisMotionValue(t),o=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(o=function(s,{min:l,max:u},d){return l!==void 0&&s<l?s=d?me(l,s,d.min):Math.max(s,l):u!==void 0&&s>u&&(s=d?me(u,s,d.max):Math.min(s,u)),s}(o,this.constraints[t],this.elastic[t])),n.set(o)}resolveConstraints(){var t;let{dragConstraints:r,dragElastic:i}=this.getProps(),a=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,n=this.constraints;r&&B(r)?this.constraints||(this.constraints=this.resolveRefConstraints()):r&&a?this.constraints=function(o,{top:s,left:l,bottom:u,right:d}){return{x:_i(o.x,l,d),y:_i(o.y,s,u)}}(a.layoutBox,r):this.constraints=!1,this.elastic=function(o=.35){return o===!1?o=0:o===!0&&(o=.35),{x:Zi(o,"left","right"),y:Zi(o,"top","bottom")}}(i),n!==this.constraints&&a&&this.constraints&&!this.hasMutatedConstraints&&Ze(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=function(s,l){let u={};return l.min!==void 0&&(u.min=l.min-s.min),l.max!==void 0&&(u.max=l.max-s.min),u}(a.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){var t;let{dragConstraints:r,onMeasureDragConstraints:i}=this.getProps();if(!r||!B(r))return!1;let a=r.current;(0,Ye.k)(a!==null,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let o=function(l,u,d){let h=tn(l,d),{scroll:p}=u;return p&&(ft(h.x,p.offset.x),ft(h.y,p.offset.y)),h}(a,n.root,this.visualElement.getTransformPagePoint()),s={x:$i((t=n.layout.layoutBox).x,o.x),y:$i(t.y,o.y)};if(i){let l=i(function({x:u,y:d}){return{top:d.min,right:u.max,bottom:d.max,left:u.min}}(s));this.hasMutatedConstraints=!!l,l&&(s=Xi(l))}return s}startAnimation(t){let{drag:r,dragMomentum:i,dragElastic:a,dragTransition:n,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),l=this.constraints||{};return Promise.all(Ze(u=>{if(!nr(u,r,this.currentDirection))return;let d=l&&l[u]||{};o&&(d={min:0,max:0});let h={type:"inertia",velocity:i?t[u]:0,bounceStiffness:a?200:1e6,bounceDamping:a?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...d};return this.startAxisValueAnimation(u,h)})).then(s)}startAxisValueAnimation(t,r){let i=this.getAxisMotionValue(t);return i.start(Er(t,i,0,r))}stopAnimation(){Ze(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ze(t=>{var r;return(r=this.getAxisMotionValue(t).animation)===null||r===void 0?void 0:r.pause()})}getAnimationState(t){var r;return(r=this.getAxisMotionValue(t).animation)===null||r===void 0?void 0:r.state}getAxisMotionValue(t){let r="_drag"+t.toUpperCase(),i=this.visualElement.getProps();return i[r]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){Ze(r=>{let{drag:i}=this.getProps();if(!nr(r,i,this.currentDirection))return;let{projection:a}=this.visualElement,n=this.getAxisMotionValue(r);if(a&&a.layout){let{min:o,max:s}=a.layout.layoutBox[r];n.set(t[r]-me(o,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:r}=this.getProps(),{projection:i}=this.visualElement;if(!B(r)||!i||!this.constraints)return;this.stopAnimation();let a={x:0,y:0};Ze(o=>{let s=this.getAxisMotionValue(o);if(s){let l=s.get();a[o]=function(u,d){let h=.5,p=We(u),c=We(d);return c>p?h=_t(d.min,d.max-p,u.min):p>c&&(h=_t(u.min,u.max-c,d.min)),qe(0,1,h)}({min:l,max:l},this.constraints[o])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),Ze(o=>{if(!nr(o,t,null))return;let s=this.getAxisMotionValue(o),{min:l,max:u}=this.constraints[o];s.set(me(l,u,a[o]))})}addListeners(){if(!this.visualElement.current)return;Fo.set(this.visualElement,this);let t=Ae(this.visualElement.current,"pointerdown",s=>{let{drag:l,dragListener:u=!0}=this.getProps();l&&u&&this.start(s)}),r=()=>{let{dragConstraints:s}=this.getProps();B(s)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,a=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();let n=$e(window,"resize",()=>this.scalePositionWithinConstraints()),o=i.addEventListener("didUpdate",({delta:s,hasLayoutChanged:l})=>{this.isDragging&&l&&(Ze(u=>{let d=this.getAxisMotionValue(u);d&&(this.originPoint[u]+=s[u].translate,d.set(d.get()+s[u].translate))}),this.visualElement.render())});return()=>{n(),t(),a(),o&&o()}}getProps(){let t=this.visualElement.getProps(),{drag:r=!1,dragDirectionLock:i=!1,dragPropagation:a=!1,dragConstraints:n=!1,dragElastic:o=.35,dragMomentum:s=!0}=t;return{...t,drag:r,dragDirectionLock:i,dragPropagation:a,dragConstraints:n,dragElastic:o,dragMomentum:s}}}function nr(e,t,r){return(t===!0||t===e)&&(r===null||r===e)}class Oo extends ct{constructor(t){super(t),this.removeGroupControls=Ee.Z,this.removeListeners=Ee.Z,this.controls=new Bo(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ee.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let nn=e=>(t,r)=>{e&&D.Wi.update(()=>e(t,r))};class No extends ct{constructor(){super(...arguments),this.removePointerDownListener=Ee.Z}onPointerDown(t){this.session=new Oi(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rn(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:r,onPan:i,onPanEnd:a}=this.node.getProps();return{onSessionStart:nn(t),onStart:nn(r),onMove:i,onEnd:(n,o)=>{delete this.session,a&&D.Wi.update(()=>a(n,o))}}}mount(){this.removePointerDownListener=Ae(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let or={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function on(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let Ht={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string"){if(!m.test(e))return e;e=parseFloat(e)}let r=on(e,t.target.x),i=on(e,t.target.y);return`${r}% ${i}%`}};class Io extends P.Component{componentDidMount(){let{visualElement:t,layoutGroup:r,switchLayoutGroup:i,layoutId:a}=this.props,{projection:n}=t;Object.assign(pe,Wo),n&&(r.group&&r.group.add(n),i&&i.register&&a&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),or.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:r,visualElement:i,drag:a,isPresent:n}=this.props,o=i.projection;return o&&(o.isPresent=n,a||t.layoutDependency!==r||r===void 0?o.willUpdate():this.safeToRemove(),t.isPresent===n||(n?o.promote():o.relegate()||D.Wi.postRender(()=>{let s=o.getStack();s&&s.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:r,switchLayoutGroup:i}=this.props,{projection:a}=t;a&&(a.scheduleCheckAfterUnmount(),r&&r.group&&r.group.remove(a),i&&i.deregister&&i.deregister(a))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sn(e){let[t,r]=function(){let a=(0,P.useContext)(ne.O);if(a===null)return[!0,null];let{isPresent:n,onExitComplete:o,register:s}=a,l=(0,P.useId)();return(0,P.useEffect)(()=>s(l),[]),!n&&o?[!1,()=>o&&o(l)]:[!0]}(),i=(0,P.useContext)(be.p);return P.createElement(Io,{...e,layoutGroup:i,switchLayoutGroup:(0,P.useContext)(Me),isPresent:t,safeToRemove:r})}let Wo={borderRadius:{...Ht,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Ht,borderTopRightRadius:Ht,borderBottomLeftRadius:Ht,borderBottomRightRadius:Ht,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let i=mt.parse(e);if(i.length>5)return e;let a=mt.createTransformer(e),n=typeof i[0]!="number"?1:0,o=r.x.scale*t.x,s=r.y.scale*t.y;i[0+n]/=o,i[1+n]/=s;let l=me(o,s,.5);return typeof i[2+n]=="number"&&(i[2+n]/=l),typeof i[3+n]=="number"&&(i[3+n]/=l),a(i)}}},an=["TopLeft","TopRight","BottomLeft","BottomRight"],zo=an.length,ln=e=>typeof e=="string"?parseFloat(e):e,un=e=>typeof e=="number"||m.test(e);function hn(e,t){return e[t]!==void 0?e[t]:e.borderRadius}let Uo=dn(0,.5,ai),_o=dn(.5,.95,Ee.Z);function dn(e,t,r){return i=>i<e?0:i>t?1:r(_t(e,t,i))}function cn(e,t){e.min=t.min,e.max=t.max}function Ge(e,t){cn(e.x,t.x),cn(e.y,t.y)}function pn(e,t,r,i,a){return e-=t,e=i+1/r*(e-i),a!==void 0&&(e=i+1/a*(e-i)),e}function mn(e,t,[r,i,a],n,o){(function(s,l=0,u=1,d=.5,h,p=s,c=s){if(Ue.test(l)&&(l=parseFloat(l),l=me(c.min,c.max,l/100)-c.min),typeof l!="number")return;let f=me(p.min,p.max,d);s===p&&(f-=l),s.min=pn(s.min,l,u,f,h),s.max=pn(s.max,l,u,f,h)})(e,t[r],t[i],t[a],t.scale,n,o)}let $o=["x","scaleX","originX"],Zo=["y","scaleY","originY"];function fn(e,t,r,i){mn(e.x,t,$o,r?r.x:void 0,i?i.x:void 0),mn(e.y,t,Zo,r?r.y:void 0,i?i.y:void 0)}function gn(e){return e.translate===0&&e.scale===1}function yn(e){return gn(e.x)&&gn(e.y)}function vn(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function bn(e){return We(e.x)/We(e.y)}class Go{constructor(){this.members=[]}add(t){Vr(this.members,t),t.scheduleRender()}remove(t){if(Mr(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let r=this.members[this.members.length-1];r&&this.promote(r)}}relegate(t){let r,i=this.members.findIndex(a=>t===a);if(i===0)return!1;for(let a=i;a>=0;a--){let n=this.members[a];if(n.isPresent!==!1){r=n;break}}return!!r&&(this.promote(r),!0)}promote(t,r){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,r&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:a}=t.options;a===!1&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:r,resumingFrom:i}=t;r.onExitComplete&&r.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function xn(e,t,r){let i="",a=e.x.translate/t.x,n=e.y.translate/t.y;if((a||n)&&(i=`translate3d(${a}px, ${n}px, 0) `),(t.x!==1||t.y!==1)&&(i+=`scale(${1/t.x}, ${1/t.y}) `),r){let{rotate:l,rotateX:u,rotateY:d}=r;l&&(i+=`rotate(${l}deg) `),u&&(i+=`rotateX(${u}deg) `),d&&(i+=`rotateY(${d}deg) `)}let o=e.x.scale*t.x,s=e.y.scale*t.y;return(o!==1||s!==1)&&(i+=`scale(${o}, ${s})`),i||"none"}let Ho=(e,t)=>e.depth-t.depth;class Yo{constructor(){this.children=[],this.isDirty=!1}add(t){Vr(this.children,t),this.isDirty=!0}remove(t){Mr(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Ho),this.isDirty=!1,this.children.forEach(t)}}let wn=["","X","Y","Z"],Xo={visibility:"hidden"},qo=0,St={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Pn({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:i,resetTransform:a}){return class{constructor(n={},o=t?.()){this.id=qo++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,St.totalNodes=St.resolvedTargetDeltas=St.recalculatedProjection=0,this.nodes.forEach(Qo),this.nodes.forEach(ns),this.nodes.forEach(os),this.nodes.forEach(es),window.MotionDebug&&window.MotionDebug.record(St)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=n,this.root=o?o.root||o:this,this.path=o?[...o.path,o]:[],this.parent=o,this.depth=o?o.depth+1:0;for(let s=0;s<this.path.length;s++)this.path[s].shouldResetTransform=!0;this.root===this&&(this.nodes=new Yo)}addEventListener(n,o){return this.eventHandlers.has(n)||this.eventHandlers.set(n,new Dr),this.eventHandlers.get(n).add(o)}notifyListeners(n,...o){let s=this.eventHandlers.get(n);s&&s.notify(...o)}hasListeners(n){return this.eventHandlers.has(n)}mount(n,o=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=n instanceof SVGElement&&n.tagName!=="svg",this.instance=n;let{layoutId:s,layout:l,visualElement:u}=this.options;if(u&&!u.current&&u.mount(n),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),o&&(l||s)&&(this.isLayoutDirty=!0),e){let d,h=()=>this.root.updateBlockedByResize=!1;e(n,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=function(p,c){let f=performance.now(),v=({timestamp:g})=>{let w=g-f;w>=250&&((0,D.Pn)(v),p(w-250))};return D.Wi.read(v,!0),()=>(0,D.Pn)(v)}(h,0),or.hasAnimatedSinceResize&&(or.hasAnimatedSinceResize=!1,this.nodes.forEach(Tn))})}s&&this.root.registerSharedNode(s,this),this.options.animate!==!1&&u&&(s||l)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:h,hasRelativeTargetChanged:p,layout:c})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let f=this.options.transition||u.getDefaultTransition()||us,{onLayoutAnimationStart:v,onLayoutAnimationComplete:g}=u.getProps(),w=!this.targetLayout||!vn(this.targetLayout,c)||p,b=!h&&p;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||b||h&&(w||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,b);let y={...Ar(f,"layout"),onPlay:v,onComplete:g};(u.shouldReduceMotion||this.options.layoutRoot)&&(y.delay=0,y.type=!1),this.startAnimation(y)}else h||Tn(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=c})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let n=this.getStack();n&&n.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,D.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ss),this.animationId++)}getTransformTemplate(){let{visualElement:n}=this.options;return n&&n.getProps().transformTemplate}willUpdate(n=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){let d=this.path[u];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}let{layoutId:o,layout:s}=this.options;if(o===void 0&&!s)return;let l=this.getTransformTemplate();this.prevTransformTemplateValue=l?l(this.latestValues,""):void 0,this.updateSnapshot(),n&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Sn);return}this.isUpdating||this.nodes.forEach(rs),this.isUpdating=!1,this.nodes.forEach(is),this.nodes.forEach(Ko),this.nodes.forEach(Jo),this.clearAllSnapshots();let n=performance.now();D.frameData.delta=qe(0,16.666666666666668,n-D.frameData.timestamp),D.frameData.timestamp=n,D.frameData.isProcessing=!0,D.S6.update.process(D.frameData),D.S6.preRender.process(D.frameData),D.S6.render.process(D.frameData),D.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(ts),this.sharedNodes.forEach(as)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,D.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){D.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let s=0;s<this.path.length;s++)this.path[s].updateScroll();let n=this.layout;this.layout=this.measure(!1),this.layoutCorrected=we(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:o}=this.options;o&&o.notify("LayoutMeasure",this.layout.layoutBox,n?n.layoutBox:void 0)}updateScroll(n="measure"){let o=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===n&&(o=!1),o&&(this.scroll={animationId:this.root.animationId,phase:n,isRoot:i(this.instance),offset:r(this.instance)})}resetTransform(){if(!a)return;let n=this.isLayoutDirty||this.shouldResetTransform,o=this.projectionDelta&&!yn(this.projectionDelta),s=this.getTransformTemplate(),l=s?s(this.latestValues,""):void 0,u=l!==this.prevTransformTemplateValue;n&&(o||Pt(this.latestValues)||u)&&(a(this.instance,l),this.shouldResetTransform=!1,this.scheduleRender())}measure(n=!0){var o;let s=this.measurePageBox(),l=this.removeElementScroll(s);return n&&(l=this.removeTransform(l)),Vn((o=l).x),Vn(o.y),{animationId:this.root.animationId,measuredBox:s,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:n}=this.options;if(!n)return we();let o=n.measureViewportBox(),{scroll:s}=this.root;return s&&(ft(o.x,s.offset.x),ft(o.y,s.offset.y)),o}removeElementScroll(n){let o=we();Ge(o,n);for(let s=0;s<this.path.length;s++){let l=this.path[s],{scroll:u,options:d}=l;if(l!==this.root&&u&&d.layoutScroll){if(u.isRoot){Ge(o,n);let{scroll:h}=this.root;h&&(ft(o.x,-h.offset.x),ft(o.y,-h.offset.y))}ft(o.x,u.offset.x),ft(o.y,u.offset.y)}}return o}applyTransform(n,o=!1){let s=we();Ge(s,n);for(let l=0;l<this.path.length;l++){let u=this.path[l];!o&&u.options.layoutScroll&&u.scroll&&u!==u.root&&Rt(s,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),Pt(u.latestValues)&&Rt(s,u.latestValues)}return Pt(this.latestValues)&&Rt(s,this.latestValues),s}removeTransform(n){let o=we();Ge(o,n);for(let s=0;s<this.path.length;s++){let l=this.path[s];if(!l.instance||!Pt(l.latestValues))continue;Or(l.latestValues)&&l.updateSnapshot();let u=we();Ge(u,l.measurePageBox()),fn(o,l.latestValues,l.snapshot?l.snapshot.layoutBox:void 0,u)}return Pt(this.latestValues)&&fn(o,this.latestValues),o}setTargetDelta(n){this.targetDelta=n,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(n){this.options={...this.options,...n,crossfade:n.crossfade===void 0||n.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==D.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(n=!1){var o,s,l,u;let d=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=d.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=d.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=d.isSharedProjectionDirty);let h=!!this.resumingFrom||this!==d;if(!(n||h&&this.isSharedProjectionDirty||this.isProjectionDirty||((o=this.parent)===null||o===void 0?void 0:o.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:p,layoutId:c}=this.options;if(this.layout&&(p||c)){if(this.resolvedRelativeTargetAt=D.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let f=this.getClosestProjectingParent();f&&f.layout&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=we(),this.relativeTargetOrigin=we(),Gt(this.relativeTargetOrigin,this.layout.layoutBox,f.layout.layoutBox),Ge(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=we(),this.targetWithTransforms=we()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),s=this.target,l=this.relativeTarget,u=this.relativeParent.target,zi(s.x,l.x,u.x),zi(s.y,l.y,u.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ge(this.target,this.layout.layoutBox),Ji(this.target,this.targetDelta)):Ge(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let f=this.getClosestProjectingParent();f&&!!f.resumingFrom==!!this.resumingFrom&&!f.options.layoutScroll&&f.target&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=we(),this.relativeTargetOrigin=we(),Gt(this.relativeTargetOrigin,this.target,f.target),Ge(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}St.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||Or(this.parent.latestValues)||qi(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var n;let o=this.getLead(),s=!!this.resumingFrom||this!==o,l=!0;if((this.isProjectionDirty||((n=this.parent)===null||n===void 0?void 0:n.isProjectionDirty))&&(l=!1),s&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===D.frameData.timestamp&&(l=!1),l)return;let{layout:u,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||d))return;Ge(this.layoutCorrected,this.layout.layoutBox);let h=this.treeScale.x,p=this.treeScale.y;(function(v,g,w,b=!1){let y,x,T=w.length;if(T){g.x=g.y=1;for(let C=0;C<T;C++){x=(y=w[C]).projectionDelta;let E=y.instance;(!E||!E.style||E.style.display!=="contents")&&(b&&y.options.layoutScroll&&y.scroll&&y!==y.root&&Rt(v,{x:-y.scroll.offset.x,y:-y.scroll.offset.y}),x&&(g.x*=x.x.scale,g.y*=x.y.scale,Ji(v,x)),b&&Pt(y.latestValues)&&Rt(v,y.latestValues))}g.x=Qi(g.x),g.y=Qi(g.y)}})(this.layoutCorrected,this.treeScale,this.path,s),o.layout&&!o.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(o.target=o.layout.layoutBox);let{target:c}=o;if(!c){this.projectionTransform&&(this.projectionDelta=jt(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=jt(),this.projectionDeltaWithTransform=jt());let f=this.projectionTransform;Zt(this.projectionDelta,this.layoutCorrected,c,this.latestValues),this.projectionTransform=xn(this.projectionDelta,this.treeScale),(this.projectionTransform!==f||this.treeScale.x!==h||this.treeScale.y!==p)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",c)),St.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(n=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),n){let o=this.getStack();o&&o.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(n,o=!1){let s,l=this.snapshot,u=l?l.latestValues:{},d={...this.latestValues},h=jt();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!o;let p=we(),c=(l?l.source:void 0)!==(this.layout?this.layout.source:void 0),f=this.getStack(),v=!f||f.members.length<=1,g=!!(c&&!v&&this.options.crossfade===!0&&!this.path.some(ls));this.animationProgress=0,this.mixTargetDelta=w=>{let b=w/1e3;if(kn(h.x,n.x,b),kn(h.y,n.y,b),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var y,x,T,C;Gt(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),T=this.relativeTarget,C=this.relativeTargetOrigin,Cn(T.x,C.x,p.x,b),Cn(T.y,C.y,p.y,b),s&&(y=this.relativeTarget,x=s,y.x.min===x.x.min&&y.x.max===x.x.max&&y.y.min===x.y.min&&y.y.max===x.y.max)&&(this.isProjectionDirty=!1),s||(s=we()),Ge(s,this.relativeTarget)}c&&(this.animationValues=d,function(E,S,F,Q,he,ie){he?(E.opacity=me(0,F.opacity!==void 0?F.opacity:1,Uo(Q)),E.opacityExit=me(S.opacity!==void 0?S.opacity:1,0,_o(Q))):ie&&(E.opacity=me(S.opacity!==void 0?S.opacity:1,F.opacity!==void 0?F.opacity:1,Q));for(let _=0;_<zo;_++){let M=`border${an[_]}Radius`,H=hn(S,M),R=hn(F,M);(H!==void 0||R!==void 0)&&(H||(H=0),R||(R=0),H===0||R===0||un(H)===un(R)?(E[M]=Math.max(me(ln(H),ln(R),Q),0),(Ue.test(R)||Ue.test(H))&&(E[M]+="%")):E[M]=R)}(S.rotate||F.rotate)&&(E.rotate=me(S.rotate||0,F.rotate||0,Q))}(d,u,this.latestValues,b,g,v)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=b},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(n){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,D.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=D.Wi.update(()=>{or.hasAnimatedSinceResize=!0,this.currentAnimation=function(o,s,l){let u=le(0)?0:Dt(0);return u.start(Er("",u,1e3,l)),u.animation}(0,0,{...n,onUpdate:o=>{this.mixTargetDelta(o),n.onUpdate&&n.onUpdate(o)},onComplete:()=>{n.onComplete&&n.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let n=this.getStack();n&&n.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let n=this.getLead(),{targetWithTransforms:o,target:s,layout:l,latestValues:u}=n;if(o&&s&&l){if(this!==n&&this.layout&&l&&Mn(this.options.animationType,this.layout.layoutBox,l.layoutBox)){s=this.target||we();let d=We(this.layout.layoutBox.x);s.x.min=n.target.x.min,s.x.max=s.x.min+d;let h=We(this.layout.layoutBox.y);s.y.min=n.target.y.min,s.y.max=s.y.min+h}Ge(o,s),Rt(o,u),Zt(this.projectionDeltaWithTransform,this.layoutCorrected,o,u)}}registerSharedNode(n,o){this.sharedNodes.has(n)||this.sharedNodes.set(n,new Go),this.sharedNodes.get(n).add(o);let s=o.options.initialPromotionConfig;o.promote({transition:s?s.transition:void 0,preserveFollowOpacity:s&&s.shouldPreserveFollowOpacity?s.shouldPreserveFollowOpacity(o):void 0})}isLead(){let n=this.getStack();return!n||n.lead===this}getLead(){var n;let{layoutId:o}=this.options;return o&&((n=this.getStack())===null||n===void 0?void 0:n.lead)||this}getPrevLead(){var n;let{layoutId:o}=this.options;return o?(n=this.getStack())===null||n===void 0?void 0:n.prevLead:void 0}getStack(){let{layoutId:n}=this.options;if(n)return this.root.sharedNodes.get(n)}promote({needsReset:n,transition:o,preserveFollowOpacity:s}={}){let l=this.getStack();l&&l.promote(this,s),n&&(this.projectionDelta=void 0,this.needsReset=!0),o&&this.setOptions({transition:o})}relegate(){let n=this.getStack();return!!n&&n.relegate(this)}resetRotation(){let{visualElement:n}=this.options;if(!n)return;let o=!1,{latestValues:s}=n;if((s.rotate||s.rotateX||s.rotateY||s.rotateZ)&&(o=!0),!o)return;let l={};for(let u=0;u<wn.length;u++){let d="rotate"+wn[u];s[d]&&(l[d]=s[d],n.setStaticValue(d,0))}for(let u in n.render(),l)n.setStaticValue(u,l[u]);n.scheduleRender()}getProjectionStyles(n){var o,s;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Xo;let l={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,l.opacity="",l.pointerEvents=se(n?.pointerEvents)||"",l.transform=u?u(this.latestValues,""):"none",l;let d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){let f={};return this.options.layoutId&&(f.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,f.pointerEvents=se(n?.pointerEvents)||""),this.hasProjected&&!Pt(this.latestValues)&&(f.transform=u?u({},""):"none",this.hasProjected=!1),f}let h=d.animationValues||d.latestValues;this.applyTransformsToTarget(),l.transform=xn(this.projectionDeltaWithTransform,this.treeScale,h),u&&(l.transform=u(h,l.transform));let{x:p,y:c}=this.projectionDelta;for(let f in l.transformOrigin=`${100*p.origin}% ${100*c.origin}% 0`,d.animationValues?l.opacity=d===this?(s=(o=h.opacity)!==null&&o!==void 0?o:this.latestValues.opacity)!==null&&s!==void 0?s:1:this.preserveOpacity?this.latestValues.opacity:h.opacityExit:l.opacity=d===this?h.opacity!==void 0?h.opacity:"":h.opacityExit!==void 0?h.opacityExit:0,pe){if(h[f]===void 0)continue;let{correct:v,applyTo:g}=pe[f],w=l.transform==="none"?h[f]:v(h[f],d);if(g){let b=g.length;for(let y=0;y<b;y++)l[g[y]]=w}else l[f]=w}return this.options.layoutId&&(l.pointerEvents=d===this?se(n?.pointerEvents)||"":"none"),l}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(n=>{var o;return(o=n.currentAnimation)===null||o===void 0?void 0:o.stop()}),this.root.nodes.forEach(Sn),this.root.sharedNodes.clear()}}}function Ko(e){e.updateLayout()}function Jo(e){var t;let r=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:a}=e.layout,{animationType:n}=e.options,o=r.source!==e.layout.source;n==="size"?Ze(h=>{let p=o?r.measuredBox[h]:r.layoutBox[h],c=We(p);p.min=i[h].min,p.max=p.min+c}):Mn(n,r.layoutBox,i)&&Ze(h=>{let p=o?r.measuredBox[h]:r.layoutBox[h],c=We(i[h]);p.max=p.min+c,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[h].max=e.relativeTarget[h].min+c)});let s=jt();Zt(s,i,r.layoutBox);let l=jt();o?Zt(l,e.applyTransform(a,!0),r.measuredBox):Zt(l,i,r.layoutBox);let u=!yn(s),d=!1;if(!e.resumeFrom){let h=e.getClosestProjectingParent();if(h&&!h.resumeFrom){let{snapshot:p,layout:c}=h;if(p&&c){let f=we();Gt(f,r.layoutBox,p.layoutBox);let v=we();Gt(v,i,c.layoutBox),vn(f,v)||(d=!0),h.options.layoutRoot&&(e.relativeTarget=v,e.relativeTargetOrigin=f,e.relativeParent=h)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:r,delta:l,layoutDelta:s,hasLayoutChanged:u,hasRelativeTargetChanged:d})}else if(e.isLead()){let{onExitComplete:i}=e.options;i&&i()}e.options.transition=void 0}function Qo(e){St.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function es(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function ts(e){e.clearSnapshot()}function Sn(e){e.clearMeasurements()}function rs(e){e.isLayoutDirty=!1}function is(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Tn(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ns(e){e.resolveTargetDelta()}function os(e){e.calcProjection()}function ss(e){e.resetRotation()}function as(e){e.removeLeadSnapshot()}function kn(e,t,r){e.translate=me(t.translate,0,r),e.scale=me(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function Cn(e,t,r,i){e.min=me(t.min,r.min,i),e.max=me(t.max,r.max,i)}function ls(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}let us={duration:.45,ease:[.4,0,.1,1]},An=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),En=An("applewebkit/")&&!An("chrome/")?Math.round:Ee.Z;function Vn(e){e.min=En(e.min),e.max=En(e.max)}function Mn(e,t,r){return e==="position"||e==="preserve-aspect"&&!Fr(bn(t),bn(r),.2)}let hs=Pn({attachResizeListener:(e,t)=>$e(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ir={current:void 0},Dn=Pn({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Ir.current){let e=new hs({});e.mount(window),e.setOptions({layoutScroll:!0}),Ir.current=e}return Ir.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),ds=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Wr(e,t,r=1){(0,Ye.k)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[i,a]=function(o){let s=ds.exec(o);if(!s)return[,];let[,l,u]=s;return[l,u]}(e);if(!i)return;let n=window.getComputedStyle(t).getPropertyValue(i);if(n){let o=n.trim();return Di(o)?parseFloat(o):o}return ze(a)?Wr(a,t,r+1):a}let cs=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),jn=e=>cs.has(e),ps=e=>Object.keys(e).some(jn),sr=e=>e===it||e===m,Rn=(e,t)=>parseFloat(e.split(", ")[t]),Ln=(e,t)=>(r,{transform:i})=>{if(i==="none"||!i)return 0;let a=i.match(/^matrix3d\((.+)\)$/);if(a)return Rn(a[1],t);{let n=i.match(/^matrix\((.+)\)$/);return n?Rn(n[1],e):0}},ms=new Set(["x","y","z"]),fs=ge.filter(e=>!ms.has(e)),Lt={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Ln(4,13),y:Ln(5,14)};Lt.translateX=Lt.x,Lt.translateY=Lt.y;let gs=(e,t,r)=>{let i=t.measureViewportBox(),a=getComputedStyle(t.current),{display:n}=a,o={};n==="none"&&t.setStaticValue("display",e.display||"block"),r.forEach(l=>{o[l]=Lt[l](i,a)}),t.render();let s=t.measureViewportBox();return r.forEach(l=>{let u=t.getValue(l);u&&u.jump(o[l]),e[l]=Lt[l](s,a)}),e},ys=(e,t,r={},i={})=>{t={...t},i={...i};let a=Object.keys(t).filter(jn),n=[],o=!1,s=[];if(a.forEach(l=>{let u,d=e.getValue(l);if(!e.hasValue(l))return;let h=r[l],p=$t(h),c=t[l];if(ht(c)){let f=c.length,v=c[0]===null?1:0;p=$t(h=c[v]);for(let g=v;g<f&&c[g]!==null;g++)u?(0,Ye.k)($t(c[g])===u,"All keyframes must be of the same type"):(u=$t(c[g]),(0,Ye.k)(u===p||sr(p)&&sr(u),"Keyframes must be of the same dimension as the current value"))}else u=$t(c);if(p!==u)if(sr(p)&&sr(u)){let f=d.get();typeof f=="string"&&d.set(parseFloat(f)),typeof c=="string"?t[l]=parseFloat(c):Array.isArray(c)&&u===m&&(t[l]=c.map(parseFloat))}else p?.transform&&u?.transform&&(h===0||c===0)?h===0?d.set(u.transform(h)):t[l]=p.transform(c):(o||(n=function(f){let v=[];return fs.forEach(g=>{let w=f.getValue(g);w!==void 0&&(v.push([g,w.get()]),w.set(g.startsWith("scale")?1:0))}),v.length&&f.render(),v}(e),o=!0),s.push(l),i[l]=i[l]!==void 0?i[l]:t[l],d.jump(c))}),!s.length)return{target:t,transitionEnd:i};{let l=s.indexOf("height")>=0?window.pageYOffset:null,u=gs(t,e,s);return n.length&&n.forEach(([d,h])=>{e.getValue(d).set(h)}),e.render(),Se.j&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:i}}},vs=(e,t,r,i)=>{let a=function(n,{...o},s){let l=n.current;if(!(l instanceof Element))return{target:o,transitionEnd:s};for(let u in s&&(s={...s}),n.values.forEach(d=>{let h=d.get();if(!ze(h))return;let p=Wr(h,l);p&&d.set(p)}),o){let d=o[u];if(!ze(d))continue;let h=Wr(d,l);h&&(o[u]=h,s||(s={}),s[u]===void 0&&(s[u]=d))}return{target:o,transitionEnd:s}}(e,t,i);return function(n,o,s,l){return ps(o)?ys(n,o,s,l):{target:o,transitionEnd:l}}(e,t=a.target,r,i=a.transitionEnd)},zr={current:null},Fn={current:!1},Bn=new WeakMap,On=Object.keys(z),bs=On.length,Nn=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],xs=oe.length;class ws{constructor({parent:t,props:r,presenceContext:i,reducedMotionConfig:a,visualState:n},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>D.Wi.render(this.render,!1,!0);let{latestValues:s,renderState:l}=n;this.latestValues=s,this.baseTarget={...s},this.initialValues=r.initial?{...s}:{},this.renderState=l,this.parent=t,this.props=r,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=a,this.options=o,this.isControllingVariants=ce(r),this.isVariantNode=te(r),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(r,{});for(let h in d){let p=d[h];s[h]!==void 0&&le(p)&&(p.set(s[h],!1),ir(u)&&u.add(h))}}scrapeMotionValuesFromProps(t,r){return{}}mount(t){this.current=t,Bn.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((r,i)=>this.bindToMotionValue(i,r)),Fn.current||function(){if(Fn.current=!0,Se.j)if(window.matchMedia){let r=window.matchMedia("(prefers-reduced-motion)"),i=()=>zr.current=r.matches;r.addListener(i),i()}else zr.current=!1}(),this.shouldReduceMotion=this.reducedMotionConfig!=="never"&&(this.reducedMotionConfig==="always"||zr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in Bn.delete(this.current),this.projection&&this.projection.unmount(),(0,D.Pn)(this.notifyUpdate),(0,D.Pn)(this.render),this.valueSubscriptions.forEach(r=>r()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,r){let i=re.has(t),a=r.on("change",o=>{this.latestValues[t]=o,this.props.onUpdate&&D.Wi.update(this.notifyUpdate,!1,!0),i&&this.projection&&(this.projection.isTransformDirty=!0)}),n=r.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{a(),n()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...r},i,a,n){let o,s;for(let l=0;l<bs;l++){let u=On[l],{isEnabled:d,Feature:h,ProjectionNode:p,MeasureLayout:c}=z[u];p&&(o=p),d(r)&&(!this.features[u]&&h&&(this.features[u]=new h(this)),c&&(s=c))}if((this.type==="html"||this.type==="svg")&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);let{layoutId:l,layout:u,drag:d,dragConstraints:h,layoutScroll:p,layoutRoot:c}=r;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!d||h&&B(h),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:n,layoutScroll:p,layoutRoot:c})}return s}updateFeatures(){for(let t in this.features){let r=this.features[t];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):we()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,r){this.latestValues[t]=r}makeTargetAnimatable(t,r=!0){return this.makeTargetAnimatableFromInstance(t,this.props,r)}update(t,r){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=r;for(let i=0;i<Nn.length;i++){let a=Nn[i];this.propEventSubscriptions[a]&&(this.propEventSubscriptions[a](),delete this.propEventSubscriptions[a]);let n=t["on"+a];n&&(this.propEventSubscriptions[a]=this.on(a,n))}this.prevMotionValues=function(i,a,n){let{willChange:o}=a;for(let s in a){let l=a[s],u=n[s];if(le(l))i.addValue(s,l),ir(o)&&o.add(s);else if(le(u))i.addValue(s,Dt(l,{owner:i})),ir(o)&&o.remove(s);else if(u!==l)if(i.hasValue(s)){let d=i.getValue(s);d.hasAnimated||d.set(l)}else{let d=i.getStaticValue(s);i.addValue(s,Dt(d!==void 0?d:l,{owner:i}))}}for(let s in n)a[s]===void 0&&i.removeValue(s);return a}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let i=this.parent&&this.parent.getVariantContext()||{};return this.props.initial!==void 0&&(i.initial=this.props.initial),i}let r={};for(let i=0;i<xs;i++){let a=oe[i],n=this.props[a];(N(n)||n===!1)&&(r[a]=n)}return r}addVariantChild(t){let r=this.getClosestVariantNode();if(r)return r.variantChildren&&r.variantChildren.add(t),()=>r.variantChildren.delete(t)}addValue(t,r){r!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,r)),this.values.set(t,r),this.latestValues[t]=r.get()}removeValue(t){this.values.delete(t);let r=this.valueSubscriptions.get(t);r&&(r(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,r){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return i===void 0&&r!==void 0&&(i=Dt(r,{owner:this}),this.addValue(t,i)),i}readValue(t){var r;return this.latestValues[t]===void 0&&this.current?(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,r){this.baseTarget[t]=r}getBaseTarget(t){var r;let{initial:i}=this.props,a=typeof i=="string"||typeof i=="object"?(r=At(this.props,i))===null||r===void 0?void 0:r[t]:void 0;if(i&&a!==void 0)return a;let n=this.getBaseTargetFromProps(this.props,t);return n===void 0||le(n)?this.initialValues[t]!==void 0&&a===void 0?void 0:this.baseTarget[t]:n}on(t,r){return this.events[t]||(this.events[t]=new Dr),this.events[t].add(r)}notify(t,...r){this.events[t]&&this.events[t].notify(...r)}}class In extends ws{sortInstanceNodePosition(t,r){return 2&t.compareDocumentPosition(r)?1:-1}getBaseTargetFromProps(t,r){return t.style?t.style[r]:void 0}removeValueFromRenderState(t,{vars:r,style:i}){delete r[t],delete i[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:r,...i},{transformValues:a},n){let o=function(s,l,u){let d={};for(let h in s){let p=function(c,f){if(f)return(f[c]||f.default||f).from}(h,l);if(p!==void 0)d[h]=p;else{let c=u.getValue(h);c&&(d[h]=c.get())}}return d}(i,t||{},this);if(a&&(r&&(r=a(r)),i&&(i=a(i)),o&&(o=a(o))),n){(function(l,u,d){var h,p;let c=Object.keys(u).filter(v=>!l.hasValue(v)),f=c.length;if(f)for(let v=0;v<f;v++){let g=c[v],w=u[g],b=null;Array.isArray(w)&&(b=w[0]),b===null&&(b=(p=(h=d[g])!==null&&h!==void 0?h:l.readValue(g))!==null&&p!==void 0?p:u[g]),b!=null&&(typeof b=="string"&&(Di(b)||Mi(b))?b=parseFloat(b):!Co(b)&&mt.test(w)&&(b=Vi(g,w)),l.addValue(g,Dt(b,{owner:l})),d[g]===void 0&&(d[g]=b),b!==null&&l.setBaseTarget(g,b))}})(this,i,o);let s=vs(this,i,o,r);r=s.transitionEnd,i=s.target}return{transition:t,transitionEnd:r,...i}}}class Ps extends In{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,r){if(re.has(r)){let i=Cr(r);return i&&i.default||0}{let i=window.getComputedStyle(t),a=(I(r)?i.getPropertyValue(r):i[r])||0;return typeof a=="string"?a.trim():a}}measureInstanceViewportBox(t,{transformPagePoint:r}){return tn(t,r)}build(t,r,i,a){X(t,r,i,a.transformTemplate)}scrapeMotionValuesFromProps(t,r){return ut(t,r)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;le(t)&&(this.childSubscription=t.on("change",r=>{this.current&&(this.current.textContent=`${r}`)}))}renderInstance(t,r,i,a){Le(t,r,i,a)}}class Ss extends In{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,r){return t[r]}readValueFromInstance(t,r){if(re.has(r)){let i=Cr(r);return i&&i.default||0}return r=lt.has(r)?r:q(r),t.getAttribute(r)}measureInstanceViewportBox(){return we()}scrapeMotionValuesFromProps(t,r){return Ot(t,r)}build(t,r,i,a){ue(t,r,i,this.isSVGTag,a.transformTemplate)}renderInstance(t,r,i,a){Qe(t,r,i,a)}mount(t){this.isSVGTag=Re(t.tagName),super.mount(t)}}let Ts=(e,t)=>fe(e)?new Ss(t,{enableHardwareAcceleration:!1}):new Ps(t,{enableHardwareAcceleration:!0}),ks={animation:{Feature:Mo},exit:{Feature:jo},inView:{Feature:Hn},tap:{Feature:_n},focus:{Feature:Un},hover:{Feature:zn},pan:{Feature:No},drag:{Feature:Oo,ProjectionNode:Dn,MeasureLayout:sn},layout:{ProjectionNode:Dn,MeasureLayout:sn}},Cs=function(e){function t(i,a={}){return function({preloadedFeatures:n,createVisualElement:o,useRender:s,useVisualState:l,Component:u}){n&&function(h){for(let p in h)z[p]={...z[p],...h[p]}}(n);let d=(0,P.forwardRef)(function(h,p){var c;let f,v={...(0,P.useContext)(Z),...h,layoutId:function({layoutId:y}){let x=(0,P.useContext)(be.p).id;return x&&y!==void 0?x+"-"+y:y}(h)},{isStatic:g}=v,w=function(y){let{initial:x,animate:T}=function(C,E){if(ce(C)){let{initial:S,animate:F}=C;return{initial:S===!1||N(S)?S:void 0,animate:N(F)?F:void 0}}return C.inherit!==!1?E:{}}(y,(0,P.useContext)(G));return(0,P.useMemo)(()=>({initial:x,animate:T}),[ve(x),ve(T)])}(h),b=l(h,g);if(!g&&Se.j){w.visualElement=function(T,C,E,S){let{visualElement:F}=(0,P.useContext)(G),Q=(0,P.useContext)(de),he=(0,P.useContext)(ne.O),ie=(0,P.useContext)(Z).reducedMotion,_=(0,P.useRef)();S=S||Q.renderer,!_.current&&S&&(_.current=S(T,{visualState:C,parent:F,props:E,presenceContext:he,blockInitialAnimation:!!he&&he.initial===!1,reducedMotionConfig:ie}));let M=_.current;(0,P.useInsertionEffect)(()=>{M&&M.update(E,he)});let H=(0,P.useRef)(!!(E[W]&&!window.HandoffComplete));return(0,ee.L)(()=>{M&&(M.render(),H.current&&M.animationState&&M.animationState.animateChanges())}),(0,P.useEffect)(()=>{M&&(M.updateFeatures(),!H.current&&M.animationState&&M.animationState.animateChanges(),H.current&&(H.current=!1,window.HandoffComplete=!0))}),M}(u,b,v,o);let y=(0,P.useContext)(Me),x=(0,P.useContext)(de).strict;w.visualElement&&(f=w.visualElement.loadFeatures(v,x,n,y))}return P.createElement(G.Provider,{value:w},f&&w.visualElement?P.createElement(f,{visualElement:w.visualElement,...v}):null,s(u,h,(c=w.visualElement,(0,P.useCallback)(y=>{y&&b.mount&&b.mount(y),c&&(y?c.mount(y):c.unmount()),p&&(typeof p=="function"?p(y):B(p)&&(p.current=y))},[c])),b,g,w.visualElement))});return d[je]=u,d}(e(i,a))}if(typeof Proxy>"u")return t;let r=new Map;return new Proxy(t,{get:(i,a)=>(r.has(a)||r.set(a,t(a)),r.get(a))})}((e,t)=>function(r,{forwardMotionProps:i=!1},a,n){return{...fe(r)?Kt:Et,preloadedFeatures:a,useRender:function(o=!1){return(s,l,u,{latestValues:d},h)=>{let p=(fe(s)?function(g,w,b,y){let x=(0,P.useMemo)(()=>{let T=xe();return ue(T,w,{enableHardwareAcceleration:!1},Re(y),g.transformTemplate),{...T.attrs,style:{...T.style}}},[w]);if(g.style){let T={};Ce(T,g.style,g),x.style={...T,...x.style}}return x}:function(g,w,b){let y={},x=function(T,C,E){let S=T.style||{},F={};return Ce(F,S,T),Object.assign(F,function({transformTemplate:Q},he,ie){return(0,P.useMemo)(()=>{let _=$();return X(_,he,{enableHardwareAcceleration:!ie},Q),Object.assign({},_.vars,_.style)},[he])}(T,C,E)),T.transformValues?T.transformValues(F):F}(g,w,b);return g.drag&&g.dragListener!==!1&&(y.draggable=!1,x.userSelect=x.WebkitUserSelect=x.WebkitTouchCallout="none",x.touchAction=g.drag===!0?"none":`pan-${g.drag==="x"?"y":"x"}`),g.tabIndex===void 0&&(g.onTap||g.onTapStart||g.whileTap)&&(y.tabIndex=0),y.style=x,y})(l,d,h,s),c={...function(g,w,b){let y={};for(let x in g)(x!=="values"||typeof g.values!="object")&&(Ie(x)||b===!0&&Ne(x)||!w&&!Ne(x)||g.draggable&&x.startsWith("onDrag"))&&(y[x]=g[x]);return y}(l,typeof s=="string",o),...p,ref:u},{children:f}=l,v=(0,P.useMemo)(()=>le(f)?f.get():f,[f]);return(0,P.createElement)(s,{...c,children:v})}}(i),createVisualElement:n,Component:r}}(e,t,ks,Ts))},Pe.__chunk_5541=(Ve,Y,k)=>{k.d(Y,{Pn:()=>ee,Wi:()=>ne,frameData:()=>de,S6:()=>q});var P=k(4552);class Z{constructor(){this.order=[],this.scheduled=new Set}add(B){if(!this.scheduled.has(B))return this.scheduled.add(B),this.order.push(B),!0}remove(B){let N=this.order.indexOf(B);N!==-1&&(this.order.splice(N,1),this.scheduled.delete(B))}clear(){this.order.length=0,this.scheduled.clear()}}let G=["prepare","read","update","preRender","render","postRender"],{schedule:ne,cancel:ee,state:de,steps:q}=function(W,B){let N=!1,U=!0,J={delta:0,timestamp:0,isProcessing:!1},oe=G.reduce((K,z)=>(K[z]=function(Se){let be=new Z,Me=new Z,je=0,Te=!1,fe=!1,pe=new WeakSet,ge={schedule:(re,ke=!1,le=!1)=>{let st=le&&Te,rt=st?be:Me;return ke&&pe.add(re),rt.add(re)&&st&&Te&&(je=be.order.length),re},cancel:re=>{Me.remove(re),pe.delete(re)},process:re=>{if(Te){fe=!0;return}if(Te=!0,[be,Me]=[Me,be],Me.clear(),je=be.order.length)for(let ke=0;ke<je;ke++){let le=be.order[ke];le(re),pe.has(le)&&(ge.schedule(le),Se())}Te=!1,fe&&(fe=!1,ge.process(re))}};return ge}(()=>N=!0),K),{}),ce=K=>oe[K].process(J),te=()=>{let K=performance.now();N=!1,J.delta=U?1e3/60:Math.max(Math.min(K-J.timestamp,40),1),J.timestamp=K,J.isProcessing=!0,G.forEach(ce),J.isProcessing=!1,N&&B&&(U=!1,W(te))},ve=()=>{N=!0,U=!0,J.isProcessing||W(te)};return{schedule:G.reduce((K,z)=>{let Se=oe[z];return K[z]=(be,Me=!1,je=!1)=>(N||ve(),Se.schedule(be,Me,je)),K},{}),cancel:K=>G.forEach(z=>oe[z].cancel(K)),state:J,steps:oe}}(typeof requestAnimationFrame<"u"?requestAnimationFrame:P.Z,!0)},Pe.__chunk_6777=(Ve,Y,k)=>{k.d(Y,{O:()=>P});let P=(0,k(9220).createContext)(null)},Pe.__chunk_1370=(Ve,Y,k)=>{k.d(Y,{p:()=>P});let P=(0,k(9220).createContext)({})},Pe.__chunk_5116=(Ve,Y,k)=>{k.d(Y,{W:()=>P});function P(){for(var Z,G,ne=0,ee="",de=arguments.length;ne<de;ne++)(Z=arguments[ne])&&(G=function q(W){var B,N,U="";if(typeof W=="string"||typeof W=="number")U+=W;else if(typeof W=="object")if(Array.isArray(W)){var J=W.length;for(B=0;B<J;B++)W[B]&&(N=q(W[B]))&&(U&&(U+=" "),U+=N)}else for(N in W)W[N]&&(U&&(U+=" "),U+=N);return U}(Z))&&(ee&&(ee+=" "),ee+=G);return ee}},Pe.__chunk_8206=(Ve,Y,k)=>{k.d(Y,{j:()=>ne});var P=k(5116);let Z=ee=>typeof ee=="boolean"?`${ee}`:ee===0?"0":ee,G=P.W,ne=(ee,de)=>q=>{var W;if(de?.variants==null)return G(ee,q?.class,q?.className);let{variants:B,defaultVariants:N}=de,U=Object.keys(B).map(oe=>{let ce=q?.[oe],te=N?.[oe];if(ce===null)return null;let ve=Z(ce)||Z(te);return B[oe][ve]}),J=q&&Object.entries(q).reduce((oe,ce)=>{let[te,ve]=ce;return ve===void 0||(oe[te]=ve),oe},{});return G(ee,U,de==null||(W=de.compoundVariants)===null||W===void 0?void 0:W.reduce((oe,ce)=>{let{class:te,className:ve,...K}=ce;return Object.entries(K).every(z=>{let[Se,be]=z;return Array.isArray(be)?be.includes({...N,...J}[Se]):{...N,...J}[Se]===be})?[...oe,te,ve]:oe},[]),q?.class,q?.className)}},Pe.__chunk_3903=(Ve,Y,k)=>{k.d(Y,{g7:()=>ee,Z8:()=>ne});var P=k(9220);function Z(W,B){if(typeof W=="function")return W(B);W!=null&&(W.current=B)}var G=k(926);function ne(W){let B=function(U){let J=P.forwardRef((oe,ce)=>{let{children:te,...ve}=oe;if(P.isValidElement(te)){let K,z,Se=(K=Object.getOwnPropertyDescriptor(te.props,"ref")?.get)&&"isReactWarning"in K&&K.isReactWarning?te.ref:(K=Object.getOwnPropertyDescriptor(te,"ref")?.get)&&"isReactWarning"in K&&K.isReactWarning?te.props.ref:te.props.ref||te.ref,be=function(Me,je){let Te={...je};for(let fe in je){let pe=Me[fe],ge=je[fe];/^on[A-Z]/.test(fe)?pe&&ge?Te[fe]=(...re)=>{let ke=ge(...re);return pe(...re),ke}:pe&&(Te[fe]=pe):fe==="style"?Te[fe]={...pe,...ge}:fe==="className"&&(Te[fe]=[pe,ge].filter(Boolean).join(" "))}return{...Me,...Te}}(ve,te.props);return te.type!==P.Fragment&&(be.ref=ce?function(...Me){return je=>{let Te=!1,fe=Me.map(pe=>{let ge=Z(pe,je);return Te||typeof ge!="function"||(Te=!0),ge});if(Te)return()=>{for(let pe=0;pe<fe.length;pe++){let ge=fe[pe];typeof ge=="function"?ge():Z(Me[pe],null)}}}}(ce,Se):Se),P.cloneElement(te,be)}return P.Children.count(te)>1?P.Children.only(null):null});return J.displayName=`${U}.SlotClone`,J}(W),N=P.forwardRef((U,J)=>{let{children:oe,...ce}=U,te=P.Children.toArray(oe),ve=te.find(q);if(ve){let K=ve.props.children,z=te.map(Se=>Se!==ve?Se:P.Children.count(K)>1?P.Children.only(null):P.isValidElement(K)?K.props.children:null);return(0,G.jsx)(B,{...ce,ref:J,children:P.isValidElement(K)?P.cloneElement(K,void 0,z):null})}return(0,G.jsx)(B,{...ce,ref:J,children:oe})});return N.displayName=`${W}.Slot`,N}var ee=ne("Slot"),de=Symbol("radix.slottable");function q(W){return P.isValidElement(W)&&typeof W.type=="function"&&"__radixId"in W.type&&W.type.__radixId===de}},Pe);export{Vs as __getNamedExports};
