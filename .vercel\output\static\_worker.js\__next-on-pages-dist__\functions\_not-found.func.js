var G=Object.defineProperty;var J=Object.getOwnPropertyDescriptor;var Z=Object.getOwnPropertyNames;var Q=Object.prototype.hasOwnProperty;var Y=(a,d)=>()=>(a&&(d=a(a=0)),d);var H=(a,d,O,f)=>{if(d&&typeof d=="object"||typeof d=="function")for(let g of Z(d))!Q.call(a,g)&&g!==O&&G(a,g,{get:()=>d[g],enumerable:!(f=J(d,g))||f.enumerable});return a},K=(a,d,O)=>(H(a,d,"default"),O&&H(O,d,"default"));var nn=a=>H(G({},"__esModule",{value:!0}),a);var L={};import*as Ec from"async_hooks";var q=Y(()=>{K(L,Ec)});import{__getNamedExports as _n}from"../../__next-on-pages-dist__/webpack/1bf780904a25d17ad815e4754dc84ae6.js";import{__getNamedExports as en}from"../../__next-on-pages-dist__/webpack/5cc6287e1f68be9d6d8c5335ea861a39.js";import{__getNamedExports as cn}from"../../__next-on-pages-dist__/manifest/537259e2ed205f5a7994cb4f05664668.js";import{__getNamedExports as tn}from"../../__next-on-pages-dist__/manifest/__RSC_SERVER_MANIFEST.js";var y=globalThis.__nextOnPagesRoutesIsolation.getProxyFor("/_not-found"),h=_n(y,y,y),sn=h.__chunk_6195,on=h.__chunk_2067,an=h.__chunk_9182,un=h.__chunk_8983,rn=h.__chunk_5228,hn=h.__chunk_2296,dn=h.__chunk_4101,kn=h.__chunk_6776,mn=h.__chunk_8042,ln=h.__chunk_3665,pn=h.__chunk_6991,fn=h.__chunk_8816,gn=h.__chunk_6631,yn=h.__chunk_4828,xn=h.__chunk_828,bn=h.__chunk_5927,En=h.__chunk_8439,vn=h.__chunk_4363,jn=h.__chunk_8264,Sn=h.__chunk_1583,Rn=h.__chunk_7908,Dn=h.__chunk_8949,Nn=h.__chunk_796,Pn=h.__chunk_1651,Tn=h.__chunk_5105,Mn=h.__chunk_9642,An=h.__chunk_8819,Cn=h.__chunk_676,n=en(y,y,y),On=n.__chunk_4315,wn=n.__chunk_8693,Fn=n.__chunk_3788,In=n.__chunk_67,Ln=n.__chunk_9224,Bn=n.__chunk_8594,$n=n.__chunk_1373,Un=n.__chunk_8605,zn=n.__chunk_5926,Wn=n.__chunk_4258,Hn=n.__chunk_3652,Kn=n.__chunk_939,Xn=n.__chunk_3090,Gn=n.__chunk_2324,qn=n.__chunk_4630,Vn=n.__chunk_6453,Jn=n.__chunk_5505,Zn=n.__chunk_8797,Qn=n.__chunk_473,Yn=n.__chunk_2416,n_=n.__chunk_2168,__=n.__chunk_5778,e_=n.__chunk_4814,c_=n.__chunk_5916,t_=n.__chunk_3774,s_=n.__chunk_2012,o_=n.__chunk_7506,a_=n.__chunk_8823,u_=n.__chunk_6083,r_=n.__chunk_9963,i_=n.__chunk_8215,h_=n.__chunk_9558,d_=n.__chunk_7274,k_=n.__chunk_6037,m_=n.__chunk_7843,l_=n.__chunk_4679,p_=n.__chunk_8631,f_=n.__chunk_9022,g_=n.__chunk_4372,y_=n.__chunk_1959,x_=n.__chunk_9912,b_=n.__chunk_402,E_=n.__chunk_6419,v_=n.__chunk_9951,j_=n.__chunk_8027,S_=n.__chunk_5975,R_=n.__chunk_7105,D_=n.__chunk_3889,N_=n.__chunk_5354,P_=n.__chunk_5918,T_=n.__chunk_3786,M_=n.__chunk_4504,A_=n.__chunk_3589,C_=n.__chunk_1958,O_=n.__chunk_9556,w_=n.__chunk_5374,F_=n.__chunk_5291,I_=n.__chunk_3130,L_=n.__chunk_6536,B_=n.__chunk_7967,$_=n.__chunk_1641,U_=n.__chunk_3655,z_=n.__chunk_3659,W_=n.__chunk_3395,H_=n.__chunk_6150,K_=n.__chunk_9141,X_=n.__chunk_7206,G_=n.__chunk_1575,q_=n.__chunk_8269,V_=n.__chunk_9319,J_=n.__chunk_9279,Z_=n.__chunk_2070,Q_=n.__chunk_2650,Y_=n.__chunk_4009,ne=n.__chunk_7081,_e=n.__chunk_1427,ee=n.__chunk_1577,ce=n.__chunk_7514,te=n.__chunk_1902,se=n.__chunk_7538,oe=n.__chunk_2343,ae=n.__chunk_9567,ue=n.__chunk_9665,re=n.__chunk_5358,ie=n.__chunk_6512,he=n.__chunk_5787,de=n.__chunk_4219,ke=n.__chunk_2042,me=n.__chunk_527,le=n.__chunk_1057,pe=n.__chunk_6588,fe=n.__chunk_3000,ge=n.__chunk_9094,ye=n.__chunk_4439,xe=n.__chunk_7580,be=n.__chunk_3933,Ee=n.__chunk_6924,ve=n.__chunk_5694,je=n.__chunk_8906,Se=n.__chunk_8359,Re=n.__chunk_5912,De=n.__chunk_9130,Ne=n.__chunk_3433,Pe=n.__chunk_4782,Te=n.__chunk_7508,Me=n.__chunk_8613,Ae=n.__chunk_7745,Ce=n.__chunk_7935,Oe=n.__chunk_7245,we=n.__chunk_7588,Fe=n.__chunk_1482,Ie=n.__chunk_7603,Le=n.__chunk_3938,Be=n.__chunk_9027,$e=n.__chunk_3785,Ue=n.__chunk_849,ze=n.__chunk_7034,We=n.__chunk_9338,He=n.__chunk_5303,Ke=n.__chunk_1514,Xe=n.__chunk_518,Ge=n.__chunk_2553,qe=n.__chunk_7106,Ve=n.__chunk_5650,Je=n.__chunk_926,Ze=n.__chunk_9220,Qe=n.__chunk_2795,Ye=n.__chunk_1112,nc=n.__chunk_2505,_c=n.__chunk_4174,ec=n.__chunk_8066,cc=n.__chunk_1880,tc=n.__chunk_6989,sc=n.__chunk_8004,oc=n.__chunk_7278,ac=n.__chunk_1982,uc=n.__chunk_4737,rc=n.__chunk_5080,ic=n.__chunk_3143,hc=n.__chunk_9646,dc=n.__chunk_5181,kc=n.__chunk_4164,mc=n.__chunk_6287,lc=n.__chunk_8655,pc=n.__chunk_9751,V=cn(y,y,y),fc=V.__NEXT_FONT_MANIFEST,gc=V.__REACT_LOADABLE_MANIFEST,yc=tn(y,y,y),xc=yc.__RSC_SERVER_MANIFEST,Dc=((a,d,O)=>(d._ENTRIES={},a.__RSC_SERVER_MANIFEST=xc,d.__RSC_MANIFEST=d.__RSC_MANIFEST||{},d.__RSC_MANIFEST["/_not-found/page"]={moduleLoading:{prefix:"/_next/",crossOrigin:null},ssrModuleMapping:{},edgeSSRModuleMapping:{80:{"*":{id:"7245",name:"*",chunks:[],async:!1}},702:{"*":{id:"1109",name:"*",chunks:[],async:!1}},1060:{"*":{id:"9027",name:"*",chunks:[],async:!1}},1956:{"*":{id:"5291",name:"*",chunks:[],async:!1}},2040:{"*":{id:"7582",name:"*",chunks:[],async:!1}},2513:{"*":{id:"9224",name:"*",chunks:[],async:!1}},2846:{"*":{id:"7034",name:"*",chunks:[],async:!1}},2972:{"*":{id:"6924",name:"*",chunks:[],async:!1}},4050:{"*":{id:"67",name:"*",chunks:[],async:!1}},4707:{"*":{id:"7603",name:"*",chunks:[],async:!1}},5154:{"*":{id:"9314",name:"*",chunks:[],async:!1}},5501:{"*":{id:"8823",name:"*",chunks:[],async:!1}},6045:{"*":{id:"9961",name:"*",chunks:[],async:!1}},6110:{"*":{id:"5975",name:"*",chunks:[],async:!1}},6423:{"*":{id:"4782",name:"*",chunks:[],async:!1}},8838:{"*":{id:"6019",name:"*",chunks:[],async:!1}},9060:{"*":{id:"4504",name:"*",chunks:[],async:!1}},9107:{"*":{id:"3785",name:"*",chunks:[],async:!1}},9698:{"*":{id:"2857",name:"*",chunks:[],async:!1}}},clientModules:{"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/app-router.js":{id:2846,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/app-router.js":{id:2846,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/client-page.js":{id:9107,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/client-page.js":{id:9107,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/error-boundary.js":{id:1060,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/error-boundary.js":{id:1060,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/layout-router.js":{id:4707,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/layout-router.js":{id:4707,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/not-found-boundary.js":{id:80,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/not-found-boundary.js":{id:80,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/components/render-from-template-context.js":{id:6423,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/components/render-from-template-context.js":{id:6423,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js":{id:1956,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/shared/lib/app-router-context.shared-runtime.js":{id:1956,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":{id:9060,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/shared/lib/hooks-client-context.shared-runtime.js":{id:9060,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js":{id:6110,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/shared/lib/loadable-context.shared-runtime.js":{id:6110,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.js":{id:5501,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/shared/lib/server-inserted-html.shared-runtime.js":{id:5501,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/src/app/about/page.tsx":{id:8838,name:"*",chunks:[],async:!1},'/mnt/d/Demo/yuming/node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"variable":"--font-sans","display":"swap"}],"variableName":"inter"}':{id:7621,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},'/mnt/d/Demo/yuming/node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Playfair_Display","arguments":[{"subsets":["latin"],"variable":"--font-serif","display":"swap"}],"variableName":"playfair"}':{id:2714,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},'/mnt/d/Demo/yuming/node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"JetBrains_Mono","arguments":[{"subsets":["latin"],"variable":"--font-mono","display":"swap"}],"variableName":"jetbrains"}':{id:7736,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},"/mnt/d/Demo/yuming/src/components/theme-provider.tsx":{id:2513,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},"/mnt/d/Demo/yuming/src/contexts/TranslationContext.tsx":{id:4050,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},"/mnt/d/Demo/yuming/src/styles/globals.css":{id:9268,name:"*",chunks:["185","static/chunks/app/layout-09cb36215331f280.js"],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/client/link.js":{id:2972,name:"*",chunks:["972","static/chunks/972-cdd33ba5872841df.js","160","static/chunks/app/not-found-ba19424f7a5329f4.js"],async:!1},"/mnt/d/Demo/yuming/node_modules/next/dist/esm/client/link.js":{id:2972,name:"*",chunks:["972","static/chunks/972-cdd33ba5872841df.js","160","static/chunks/app/not-found-ba19424f7a5329f4.js"],async:!1},"/mnt/d/Demo/yuming/src/app/privacy/page.tsx":{id:6045,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/src/app/page.tsx":{id:5154,name:"*",chunks:["670","static/chunks/670-5dd29538f44abde2.js","368","static/chunks/368-5a707ce5913f81e8.js","776","static/chunks/776-654c42c107014a82.js","821","static/chunks/821-de5aadc85bdb1304.js","85","static/chunks/85-5d406691b0db5af7.js","931","static/chunks/app/page-e748b76e7fffc821.js"],async:!1},"/mnt/d/Demo/yuming/src/app/domain/[domain]/page.tsx":{id:702,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/src/app/search/page.tsx":{id:2040,name:"*",chunks:[],async:!1},"/mnt/d/Demo/yuming/src/app/terms/page.tsx":{id:9698,name:"*",chunks:[],async:!1}},entryCSSFiles:{"/mnt/d/Demo/yuming/src/":[],"/mnt/d/Demo/yuming/src/app/layout":["static/css/dfba9f4c0a8ab98e.css"],"/mnt/d/Demo/yuming/src/app/not-found":[],"/mnt/d/Demo/yuming/src/app/page":[],"/mnt/d/Demo/yuming/src/app/_not-found/page":[]}},a.__BUILD_MANIFEST={polyfillFiles:["static/chunks/polyfills-42372ed130431b0a.js"],devFiles:[],ampDevFiles:[],lowPriorityFiles:[],rootMainFiles:["static/chunks/webpack-08a9a15af710214c.js","static/chunks/fd9d1056-eabcefd8a17f0848.js","static/chunks/30-742145ca5a668fc1.js","static/chunks/main-app-1c9d1c6d6f88634c.js"],pages:{"/_app":["static/chunks/webpack-08a9a15af710214c.js","static/chunks/framework-f66176bb897dc684.js","static/chunks/main-bdc53fed2c1579dd.js","static/chunks/pages/_app-72b849fbd24ac258.js"],"/_error":["static/chunks/webpack-08a9a15af710214c.js","static/chunks/framework-f66176bb897dc684.js","static/chunks/main-bdc53fed2c1579dd.js","static/chunks/pages/_error-7ba65e1336b92748.js"]},ampFirstPages:[]},a.__BUILD_MANIFEST.lowPriorityFiles=["/static/L1ERtjXbxPN4Alx9vZK5J/_buildManifest.js",,"/static/L1ERtjXbxPN4Alx9vZK5J/_ssgManifest.js"],a.__REACT_LOADABLE_MANIFEST=gc,a.__NEXT_FONT_MANIFEST=fc,a.__INTERCEPTION_ROUTE_REWRITE_MANIFEST="[]",(()=>{"use strict";var f={},g={};function e(c){var u=g[c];if(u!==void 0)return u.exports;var t=g[c]={exports:{}},m=!0;try{f[c](t,t.exports,e),m=!1}finally{m&&delete g[c]}return t.exports}e.m=f,e.amdO={},(()=>{var c=[];e.O=(u,t,m,l)=>{if(t){l=l||0;for(var k=c.length;k>0&&c[k-1][2]>l;k--)c[k]=c[k-1];c[k]=[t,m,l];return}for(var p=1/0,k=0;k<c.length;k++){for(var[t,m,l]=c[k],x=!0,v=0;v<t.length;v++)p>=l&&Object.keys(e.O).every(A=>e.O[A](t[v]))?t.splice(v--,1):(x=!1,l<p&&(p=l));if(x){c.splice(k--,1);var r=m();r!==void 0&&(u=r)}}return u}})(),e.n=c=>{var u=c&&c.__esModule?()=>c.default:()=>c;return e.d(u,{a:u}),u},(()=>{var c,u=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__;e.t=function(t,m){if(1&m&&(t=this(t)),8&m||typeof t=="object"&&t&&(4&m&&t.__esModule||16&m&&typeof t.then=="function"))return t;var l=Object.create(null);e.r(l);var k={};c=c||[null,u({}),u([]),u(u)];for(var p=2&m&&t;typeof p=="object"&&!~c.indexOf(p);p=u(p))Object.getOwnPropertyNames(p).forEach(x=>k[x]=()=>t[x]);return k.default=()=>t,e.d(l,k),l}})(),e.d=(c,u)=>{for(var t in u)e.o(u,t)&&!e.o(c,t)&&Object.defineProperty(c,t,{enumerable:!0,get:u[t]})},e.e=()=>Promise.resolve(),e.g=function(){if(typeof d=="object")return d;try{return this||Function("return this")()}catch{if(typeof window=="object")return window}}(),e.o=(c,u)=>Object.prototype.hasOwnProperty.call(c,u),e.r=c=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},(()=>{var c={993:0};e.O.j=m=>c[m]===0;var u=(m,l)=>{var k,p,[x,v,r]=l,D=0;if(x.some(F=>c[F]!==0)){for(k in v)e.o(v,k)&&(e.m[k]=v[k]);if(r)var w=r(e)}for(m&&m(l);D<x.length;D++)p=x[D],e.o(c,p)&&c[p]&&c[p][0](),c[p]=0;return e.O(w)},t=a.webpackChunk_N_E=a.webpackChunk_N_E||[];t.forEach(u.bind(null,0)),t.push=u.bind(null,t.push.bind(t))})()})(),(a.webpackChunk_N_E=a.webpackChunk_N_E||[]).push([[294],{676:Cn,8819:An,9642:Mn,5105:Tn,1651:Pn,796:Nn,8949:Dn,7908:Rn,1583:Sn,8264:jn,4363:vn,8439:En,5927:bn,828:xn,4828:yn,6631:gn,8816:fn,6991:pn,3665:ln,8042:mn,6776:kn,4101:dn,2296:hn,5228:rn,8983:un,9182:an}]),(a.webpackChunk_N_E=a.webpackChunk_N_E||[]).push([[714],{9751:pc,8655:lc,6287:mc,4164:kc,5181:dc,9646:hc,3143:ic,5080:rc,4737:uc,1982:ac,7278:oc,8004:sc,6989:tc,1880:cc,8066:ec,4174:_c,2505:nc,1112:Ye,2795:Qe,9220:Ze,926:Je,5650:Ve,7106:qe,2553:Ge,518:Xe,1514:Ke,5303:He,9338:We,7034:ze,849:Ue,3785:$e,9027:Be,3938:Le,7603:Ie,1482:Fe,7588:we,7245:Oe,7935:Ce,7745:Ae,8613:Me,7508:Te,4782:Pe,3433:Ne,9130:De,5912:Re,8359:Se,8906:je,5694:ve,6924:Ee,3933:be,7580:xe,4439:ye,9094:ge,3e3:fe,6588:pe,1057:le,527:me,2042:ke,4219:de,5787:he,6512:ie,5358:re,9665:ue,9567:ae,2343:oe,7538:se,1902:te,7514:ce,1577:ee,1427:_e,7081:ne,4009:Y_,2650:Q_,2070:Z_,9279:J_,9319:V_,8269:q_,1575:G_,7206:X_,9141:K_,6150:H_,3395:W_,3659:z_,3655:U_,1641:$_,7967:B_,6536:L_,3130:I_,5291:F_,5374:w_,9556:O_,1958:C_,3589:A_,4504:M_,3786:T_,5918:P_,5354:N_,3889:D_,7105:R_,5975:S_,8027:j_,9951:v_,6419:E_,402:b_,9912:x_,1959:y_,4372:g_,9022:f_,8631:p_,4679:l_,7843:m_,6037:k_,7274:d_,9558:h_,8215:i_,9963:r_,6083:u_,8823:a_,7506:o_,2012:s_,3774:t_,5916:c_,4814:e_,5778:__,2168:n_,2416:Yn,473:Qn,8797:Zn,5505:Jn,6453:Vn,4630:qn,2324:Gn,3090:Xn,939:Kn,3652:Hn,4258:Wn,5926:zn}]),(a.webpackChunk_N_E=a.webpackChunk_N_E||[]).push([[628],{8605:Un,1373:$n,8594:Bn,9224:Ln,67:In,3788:Fn,8693:wn,4315:()=>{}}]),(a.webpackChunk_N_E=a.webpackChunk_N_E||[]).push([[409],{2067:on,6195:sn,6660:(f,g,e)=>{"use strict";e.r(g),e.d(g,{ComponentMod:()=>o,default:()=>s});var c,u={};e.r(u),e.d(u,{AppRouter:()=>r.WY,ClientPageRoot:()=>r.b1,GlobalError:()=>v.ZP,LayoutRouter:()=>r.yO,NotFoundBoundary:()=>r.O4,Postpone:()=>r.hQ,RenderFromTemplateContext:()=>r.b5,__next_app__:()=>I,actionAsyncStorage:()=>r.Wz,createDynamicallyTrackedSearchParams:()=>r.rL,createUntrackedSearchParams:()=>r.S5,decodeAction:()=>r.Hs,decodeFormState:()=>r.dH,decodeReply:()=>r.kf,originalPathname:()=>F,pages:()=>w,patchFetch:()=>r.XH,preconnect:()=>r.$P,preloadFont:()=>r.C5,preloadStyle:()=>r.oH,renderToReadableStream:()=>r.aW,requestAsyncStorage:()=>r.Fg,routeModule:()=>A,serverHooks:()=>r.GP,staticGenerationAsyncStorage:()=>r.AT,taintObjectReference:()=>r.nr,tree:()=>D}),e(7206);var t=e(9319),m=e(518),l=e(1902),k=e(2042),p=e(4630),x=e(4828),v=e(5505),r=e(6453);let D=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(e.bind(e,8693)),"/mnt/d/Demo/yuming/src/app/not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(e.bind(e,3788)),"/mnt/d/Demo/yuming/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(e.bind(e,8693)),"/mnt/d/Demo/yuming/src/app/not-found.tsx"]}],w=[],F="/_not-found/page",I={require:e,loadChunk:()=>Promise.resolve()},A=new p.AppPageRouteModule({definition:{kind:x.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:D}});var B=e(9094),$=e(5787),U=e(527);let C=b=>b?JSON.parse(b):void 0,z=a.__BUILD_MANIFEST,W=C(a.__REACT_LOADABLE_MANIFEST),j=(c=a.__RSC_MANIFEST)==null?void 0:c["/_not-found/page"],P=C(a.__RSC_SERVER_MANIFEST),S=C(a.__NEXT_FONT_MANIFEST),_=C(a.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];j&&P&&(0,$.Mo)({clientReferenceManifest:j,serverActionsManifest:P,serverModuleMap:(0,U.w)({serverActionsManifest:P,pageName:"/_not-found/page"})});let i=(0,m.d)({pagesType:B.s.APP,dev:!1,page:"/_not-found/page",appMod:null,pageMod:u,errorMod:null,error500Mod:null,Document:null,buildManifest:z,renderToHTML:k.f,reactLoadableManifest:W,clientReferenceManifest:j,serverActionsManifest:P,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{RDAP_CACHE_TTL:"3600",RDAP_TIMEOUT:"2500"},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:52428800,configOrigin:"next.config.js",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,analyticsId:"",images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:604800,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1},devIndicators:{buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!0,i18n:null,productionBrowserSourceMaps:!1,optimizeFonts:!0,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,httpAgentOptions:{keepAlive:!0},outputFileTracing:!0,staticPageGenerationTimeout:60,swcMinify:!0,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},experimental:{multiZoneDraftMode:!1,prerenderEarlyExit:!1,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:15,memoryBasedWorkersCount:!1,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,outputFileTracingRoot:"/mnt/d/Demo/yuming",swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,adjustFontFallbacks:!1,adjustFontFallbacksWithSizeAdjust:!1,typedRoutes:!1,instrumentationHook:!1,bundlePagesExternals:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,missingSuspenseWithCSRBailout:!0,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:30,static:300},optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},configFile:"/mnt/d/Demo/yuming/next.config.js",configFileName:"next.config.js",compiler:{removeConsole:{exclude:["error","warn"]}}},buildId:"L1ERtjXbxPN4Alx9vZK5J",nextFontManifest:S,incrementalCacheHandler:null,interceptionRouteRewrites:_}),o=u;function s(b){return(0,t.C)({...b,IncrementalCache:l.k,handler:i})}},2286:On},f=>{var g=c=>f(f.s=c);f.O(0,[294,714,628],()=>g(6660));var e=f.O();(d._ENTRIES=typeof d._ENTRIES>"u"?{}:d._ENTRIES)["middleware_app/_not-found/page"]=e}]),function(){let f={exports:{},loaded:!1};return function(e,c){var u=Object.create,t=Object.defineProperty,m=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyNames,k=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty,x=_=>t(_,"__esModule",{value:!0}),v=(_,i)=>{x(_);for(var o in i)t(_,o,{get:i[o],enumerable:!0})},r=(_,i,o)=>{if(i&&typeof i=="object"||typeof i=="function")for(let s of l(i))!p.call(_,s)&&s!=="default"&&t(_,s,{get:()=>i[s],enumerable:!(o=m(i,s))||o.enumerable});return _},D=_=>r(x(t(_!=null?u(k(_)):{},"default",_&&_.__esModule&&"default"in _?{get:()=>_.default,enumerable:!0}:{value:_,enumerable:!0})),_);v(c,{default:()=>W});var w=D((q(),nn(L))),F="@next/request-context",I=Symbol.for(F),A=Symbol.for("internal.storage");function B(){let _=d;if(!_[I]){let i=new w.AsyncLocalStorage,o={get:()=>i.getStore(),[A]:i};_[I]=o}return _[I]}var $=B();function U(_,i){return $[A].run(_,i)}function C(_){let i={};return _&&_.forEach((o,s)=>{i[s]=o,s.toLowerCase()==="set-cookie"&&(i[s]=z(o))}),i}function z(_){let i=[],o=0,s,b,E,T,M;function R(){for(;o<_.length&&/\s/.test(_.charAt(o));)o+=1;return o<_.length}function N(){return b=_.charAt(o),b!=="="&&b!==";"&&b!==","}for(;o<_.length;){for(s=o,M=!1;R();)if(b=_.charAt(o),b===","){for(E=o,o+=1,R(),T=o;o<_.length&&N();)o+=1;o<_.length&&_.charAt(o)==="="?(M=!0,o=T,i.push(_.substring(s,E)),s=o):o=E+1}else o+=1;(!M||o>=_.length)&&i.push(_.substring(s,_.length))}return i}function W(_){let i=_.staticRoutes.map(s=>({regexp:new RegExp(s.namedRegex),page:s.page})),o=_.dynamicRoutes?.map(s=>({regexp:new RegExp(s.namedRegex),page:s.page}))||[];return async function(s,b){let E=new URL(s.url).pathname,T={};if(_.nextConfig?.basePath&&E.startsWith(_.nextConfig.basePath)&&(E=E.replace(_.nextConfig.basePath,"")||"/"),_.nextConfig?.i18n)for(let R of _.nextConfig.i18n.locales){let N=new RegExp(`^/${R}($|/)`,"i");if(E.match(N)){E=E.replace(N,"/")||"/";break}}for(let R of i)if(R.regexp.exec(E)){T.name=R.page;break}if(!T.name){let R=P(E);for(let N of o||[]){if(R&&!P(N.page))continue;let X=N.regexp.exec(E);if(X){T={name:N.page,params:X.groups};break}}}let M=await U({waitUntil:b.waitUntil},()=>d._ENTRIES[`middleware_${_.name}`].default.call({},{request:{url:s.url,method:s.method,headers:C(s.headers),ip:j(s.headers,S.Ip),geo:{city:j(s.headers,S.City,!0),country:j(s.headers,S.Country,!0),latitude:j(s.headers,S.Latitude),longitude:j(s.headers,S.Longitude),region:j(s.headers,S.Region,!0)},nextConfig:_.nextConfig,page:T,body:s.body}}));return M.waitUntil&&b.waitUntil(M.waitUntil),M.response}}function j(_,i,o=!1){let s=_.get(i)||void 0;return o&&s?decodeURIComponent(s):s}function P(_){return _==="/api"||_.startsWith("/api/")}var S;(function(_){_.City="x-vercel-ip-city",_.Country="x-vercel-ip-country",_.Ip="x-real-ip",_.Latitude="x-vercel-ip-latitude",_.Longitude="x-vercel-ip-longitude",_.Region="x-vercel-ip-country-region"})(S||(S={}))}(f,f.exports),f.exports}.call({}).default({name:"app/_not-found/page",staticRoutes:[{page:"/",regex:"^/(?:/)?$",routeKeys:{},namedRegex:"^/(?:/)?$"},{page:"/_not-found",regex:"^/_not\\-found(?:/)?$",routeKeys:{},namedRegex:"^/_not\\-found(?:/)?$"},{page:"/about",regex:"^/about(?:/)?$",routeKeys:{},namedRegex:"^/about(?:/)?$"},{page:"/privacy",regex:"^/privacy(?:/)?$",routeKeys:{},namedRegex:"^/privacy(?:/)?$"},{page:"/search",regex:"^/search(?:/)?$",routeKeys:{},namedRegex:"^/search(?:/)?$"},{page:"/sitemap.xml",regex:"^/sitemap\\.xml(?:/)?$",routeKeys:{},namedRegex:"^/sitemap\\.xml(?:/)?$"},{page:"/terms",regex:"^/terms(?:/)?$",routeKeys:{},namedRegex:"^/terms(?:/)?$"}],dynamicRoutes:[{page:"/api/domain/[domain]",regex:"^/api/domain/([^/]+?)(?:/)?$",routeKeys:{nxtPdomain:"nxtPdomain"},namedRegex:"^/api/domain/(?<nxtPdomain>[^/]+?)(?:/)?$"},{page:"/domain/[domain]",regex:"^/domain/([^/]+?)(?:/)?$",routeKeys:{nxtPdomain:"nxtPdomain"},namedRegex:"^/domain/(?<nxtPdomain>[^/]+?)(?:/)?$"}],nextConfig:{basePath:""}})))(y,y,y);export{Dc as default};
