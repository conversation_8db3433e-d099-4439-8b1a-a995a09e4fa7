var ct=Object.defineProperty;var gt=Object.getOwnPropertyDescriptor;var mt=Object.getOwnPropertyNames;var kt=Object.prototype.hasOwnProperty;var yt=(l,v)=>()=>(l&&(v=l(l=0)),v);var et=(l,v,K,A)=>{if(v&&typeof v=="object"||typeof v=="function")for(let N of mt(v))!kt.call(l,N)&&N!==K&&ct(l,N,{get:()=>v[N],enumerable:!(A=gt(v,N))||A.enumerable});return l},nt=(l,v,K)=>(et(l,v,"default"),K&&et(K,v,"default"));var vt=l=>et(ct({},"__esModule",{value:!0}),l);var X={};import*as Re from"async_hooks";var ot=yt(()=>{nt(X,Re)});import{__getNamedExports as bt}from"../../../../__next-on-pages-dist__/webpack/8754.js";import{__getNamedExports as xt}from"../../../../__next-on-pages-dist__/webpack/1bf780904a25d17ad815e4754dc84ae6.js";import{__getNamedExports as wt}from"../../../../__next-on-pages-dist__/webpack/9399a0445303f296e54438349fdd2b7b.js";import{__getNamedExports as Et}from"../../../../__next-on-pages-dist__/manifest/537259e2ed205f5a7994cb4f05664668.js";var P=globalThis.__nextOnPagesRoutesIsolation.getProxyFor("/api/domain/[domain]"),At=bt(P,P,P),Nt=At.__chunk_8754,h=xt(P,P,P),Pt=h.__chunk_6195,Rt=h.__chunk_2067,jt=h.__chunk_9182,Ot=h.__chunk_8983,$t=h.__chunk_5228,Dt=h.__chunk_2296,St=h.__chunk_4101,Tt=h.__chunk_6776,Ct=h.__chunk_8042,It=h.__chunk_3665,Ft=h.__chunk_6991,Mt=h.__chunk_8816,Ut=h.__chunk_6631,Lt=h.__chunk_4828,qt=h.__chunk_828,Ht=h.__chunk_5927,Kt=h.__chunk_8439,Bt=h.__chunk_4363,Wt=h.__chunk_8264,Xt=h.__chunk_1583,zt=h.__chunk_7908,Gt=h.__chunk_8949,Zt=h.__chunk_796,Jt=h.__chunk_1651,Qt=h.__chunk_5105,Vt=h.__chunk_9642,Yt=h.__chunk_8819,te=h.__chunk_676,m=wt(P,P,P),ee=m.__chunk_1730,ne=m.__chunk_3818,re=m.__chunk_7037,ae=m.__chunk_5577,se=m.__chunk_1863,ie=m.__chunk_3039,ce=m.__chunk_9573,oe=m.__chunk_7701,pe=m.__chunk_662,_e=m.__chunk_7444,de=m.__chunk_4591,ue=m.__chunk_932,he=m.__chunk_5234,le=m.__chunk_2561,fe=m.__chunk_2988,ge=m.__chunk_2039,me=m.__chunk_9985,ke=m.__chunk_4155,ye=m.__chunk_5028,ve=m.__chunk_9548,be=m.__chunk_6914,xe=m.__chunk_7960,we=m.__chunk_5996,Ee=m.__chunk_4337,pt=Et(P,P,P),Ae=pt.__NEXT_FONT_MANIFEST,Ne=pt.__REACT_LOADABLE_MANIFEST,Se=((l,v,K)=>(v._ENTRIES={},l.__BUILD_MANIFEST={polyfillFiles:["static/chunks/polyfills-42372ed130431b0a.js"],devFiles:[],ampDevFiles:[],lowPriorityFiles:[],rootMainFiles:["static/chunks/webpack-08a9a15af710214c.js","static/chunks/fd9d1056-eabcefd8a17f0848.js","static/chunks/30-742145ca5a668fc1.js","static/chunks/main-app-1c9d1c6d6f88634c.js"],pages:{"/_app":["static/chunks/webpack-08a9a15af710214c.js","static/chunks/framework-f66176bb897dc684.js","static/chunks/main-bdc53fed2c1579dd.js","static/chunks/pages/_app-72b849fbd24ac258.js"],"/_error":["static/chunks/webpack-08a9a15af710214c.js","static/chunks/framework-f66176bb897dc684.js","static/chunks/main-bdc53fed2c1579dd.js","static/chunks/pages/_error-7ba65e1336b92748.js"]},ampFirstPages:[]},l.__BUILD_MANIFEST.lowPriorityFiles=["/static/L1ERtjXbxPN4Alx9vZK5J/_buildManifest.js",,"/static/L1ERtjXbxPN4Alx9vZK5J/_ssgManifest.js"],l.__REACT_LOADABLE_MANIFEST=Ne,l.__NEXT_FONT_MANIFEST=Ae,l.__INTERCEPTION_ROUTE_REWRITE_MANIFEST="[]",(()=>{"use strict";var A={},N={};function s(e){var _=N[e];if(_!==void 0)return _.exports;var n=N[e]={exports:{}},b=!0;try{A[e](n,n.exports,s),b=!1}finally{b&&delete N[e]}return n.exports}s.m=A,s.amdO={},(()=>{var e=[];s.O=(_,n,b,w)=>{if(n){w=w||0;for(var f=e.length;f>0&&e[f-1][2]>w;f--)e[f]=e[f-1];e[f]=[n,b,w];return}for(var k=1/0,f=0;f<e.length;f++){for(var[n,b,w]=e[f],E=!0,R=0;R<n.length;R++)k>=w&&Object.keys(s.O).every(M=>s.O[M](n[R]))?n.splice(R--,1):(E=!1,w<k&&(k=w));if(E){e.splice(f--,1);var $=b();$!==void 0&&(_=$)}}return _}})(),s.n=e=>{var _=e&&e.__esModule?()=>e.default:()=>e;return s.d(_,{a:_}),_},(()=>{var e,_=Object.getPrototypeOf?n=>Object.getPrototypeOf(n):n=>n.__proto__;s.t=function(n,b){if(1&b&&(n=this(n)),8&b||typeof n=="object"&&n&&(4&b&&n.__esModule||16&b&&typeof n.then=="function"))return n;var w=Object.create(null);s.r(w);var f={};e=e||[null,_({}),_([]),_(_)];for(var k=2&b&&n;typeof k=="object"&&!~e.indexOf(k);k=_(k))Object.getOwnPropertyNames(k).forEach(E=>f[E]=()=>n[E]);return f.default=()=>n,s.d(w,f),w}})(),s.d=(e,_)=>{for(var n in _)s.o(_,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:_[n]})},s.e=()=>Promise.resolve(),s.g=function(){if(typeof v=="object")return v;try{return this||Function("return this")()}catch{if(typeof window=="object")return window}}(),s.o=(e,_)=>Object.prototype.hasOwnProperty.call(e,_),s.r=e=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={993:0};s.O.j=b=>e[b]===0;var _=(b,w)=>{var f,k,[E,R,$]=w,F=0;if(E.some(H=>e[H]!==0)){for(f in R)s.o(R,f)&&(s.m[f]=R[f]);if($)var q=$(s)}for(b&&b(w);F<E.length;F++)k=E[F],s.o(e,k)&&e[k]&&e[k][0](),e[k]=0;return s.O(q)},n=l.webpackChunk_N_E=l.webpackChunk_N_E||[];n.forEach(_.bind(null,0)),n.push=_.bind(null,n.push.bind(n))})()})(),(l.webpackChunk_N_E=l.webpackChunk_N_E||[]).push([[294],{676:te,8819:Yt,9642:Vt,5105:Qt,1651:Jt,796:Zt,8949:Gt,7908:zt,1583:Xt,8264:Wt,4363:Bt,8439:Kt,5927:Ht,828:qt,4828:Lt,6631:Ut,8816:Mt,6991:Ft,3665:It,8042:Ct,6776:Tt,4101:St,2296:Dt,5228:$t,8983:Ot,9182:jt}]),(l.webpackChunk_N_E=l.webpackChunk_N_E||[]).push([[809],{4337:Ee,5996:we,7960:xe,6914:be,9548:ve,5028:ye,4155:ke,9985:me,2039:ge,2988:fe,2561:le,5234:he,932:ue,4591:de,7444:_e,662:pe,7701:oe,9573:ce,3039:ie,1863:se,5577:ae,7037:re,3818:ne,1730:ee}]),(l.webpackChunk_N_E=l.webpackChunk_N_E||[]).push([[877],{2067:Rt,6195:Pt,5074:(A,N,s)=>{"use strict";s.r(N),s.d(N,{ComponentMod:()=>i,default:()=>S});var e={};s.r(e),s.d(e,{GET:()=>Y,runtime:()=>R});var _={};s.r(_),s.d(_,{originalPathname:()=>d,patchFetch:()=>c,requestAsyncStorage:()=>G,routeModule:()=>C,serverHooks:()=>t,staticGenerationAsyncStorage:()=>D});var n=s(932),b=s(2561),w=s(4828),f=s(6631),k=s(9985),E=s(8754);let R="edge",$=null,F=0,q=new Map,H=new Map,B={com:["https://rdap.verisign.com/com/v1/","https://rdap.nic.com/"],net:["https://rdap.verisign.com/net/v1/","https://rdap.nic.net/"],org:["https://rdap.publicinterestregistry.org/","https://rdap.nic.org/"],info:["https://rdap.afilias.info/rdap/afilias/","https://rdap.nic.info/"],biz:["https://rdap.afilias.info/rdap/afilias/","https://rdap.nic.biz/"],name:["https://rdap.afilias.info/rdap/afilias/","https://rdap.nic.name/"],io:["https://rdap.nic.io/","https://rdap.nic.io/"],ai:["https://rdap.nic.ai/","https://rdap.nic.ai/"],co:["https://rdap.nic.co/","https://rdap.nic.co/"],me:["https://rdap.nic.me/","https://rdap.nic.me/"],tv:["https://rdap.nic.tv/","https://rdap.nic.tv/"],cc:["https://rdap.nic.cc/","https://rdap.nic.cc/"],ly:["https://rdap.nic.ly/","https://rdap.nic.ly/"],sh:["https://rdap.nic.sh/","https://rdap.nic.sh/"],gg:["https://rdap.nic.gg/","https://rdap.nic.gg/"],tech:["https://rdap.nic.tech/","https://rdap.nic.tech/"],online:["https://rdap.nic.online/","https://rdap.nic.online/"],site:["https://rdap.nic.site/","https://rdap.nic.site/"],website:["https://rdap.nic.website/","https://rdap.nic.website/"],app:["https://rdap.nic.google/","https://rdap.google.com/"],dev:["https://rdap.nic.google/","https://rdap.google.com/"],page:["https://rdap.nic.google/","https://rdap.google.com/"],how:["https://rdap.nic.google/","https://rdap.google.com/"],company:["https://rdap.nic.company/","https://rdap.nic.company/"],business:["https://rdap.nic.business/","https://rdap.nic.business/"],services:["https://rdap.nic.services/","https://rdap.nic.services/"],shop:["https://rdap.nic.shop/","https://rdap.nic.shop/"],store:["https://rdap.nic.store/","https://rdap.nic.store/"],design:["https://rdap.nic.design/","https://rdap.nic.design/"],art:["https://rdap.nic.art/","https://rdap.nic.art/"],studio:["https://rdap.nic.studio/","https://rdap.nic.studio/"],photography:["https://rdap.nic.photography/","https://rdap.nic.photography/"],blog:["https://rdap.nic.blog/","https://rdap.nic.blog/"],news:["https://rdap.nic.news/","https://rdap.nic.news/"],media:["https://rdap.nic.media/","https://rdap.nic.media/"],uk:["https://rdap.nominet.uk/","https://rdap.nic.uk/"],de:["https://rdap.denic.de/","https://rdap.nic.de/"],cn:["https://rdap.cnnic.cn/","https://rdap.nic.cn/"],nl:["https://rdap.sidn.nl/","https://rdap.nic.nl/"],fr:["https://rdap.nic.fr/","https://rdap.nic.fr/"],it:["https://rdap.nic.it/","https://rdap.nic.it/"],be:["https://rdap.dns.be/","https://rdap.nic.be/"],eu:["https://rdap.eu/","https://rdap.nic.eu/"],ca:["https://rdap.ca/","https://rdap.nic.ca/"],au:["https://rdap.auda.org.au/","https://rdap.nic.au/"],jp:["https://rdap.jprs.jp/","https://rdap.nic.jp/"],kr:["https://rdap.kr/","https://rdap.nic.kr/"],in:["https://rdap.registry.in/","https://rdap.nic.in/"],ru:["https://rdap.tcinet.ru/","https://rdap.nic.ru/"],br:["https://rdap.registro.br/","https://rdap.nic.br/"],mx:["https://rdap.mx/","https://rdap.nic.mx/"],top:["https://rdap.nic.top/","https://rdap.nic.top/"],xyz:["https://rdap.nic.xyz/","https://rdap.nic.xyz/"],click:["https://rdap.nic.click/","https://rdap.nic.click/"],link:["https://rdap.uniregistry.net/","https://rdap.nic.link/"],club:["https://rdap.nic.club/","https://rdap.nic.club/"],finance:["https://rdap.nic.finance/","https://rdap.nic.finance/"],money:["https://rdap.nic.money/","https://rdap.nic.money/"],crypto:["https://rdap.nic.crypto/","https://rdap.nic.crypto/"],games:["https://rdap.nic.games/","https://rdap.nic.games/"],fun:["https://rdap.nic.fun/","https://rdap.nic.fun/"],live:["https://rdap.nic.live/","https://rdap.nic.live/"],stream:["https://rdap.nic.stream/","https://rdap.nic.stream/"],health:["https://rdap.nic.health/","https://rdap.nic.health/"],care:["https://rdap.nic.care/","https://rdap.nic.care/"],fitness:["https://rdap.nic.fitness/","https://rdap.nic.fitness/"],education:["https://rdap.nic.education/","https://rdap.nic.education/"],academy:["https://rdap.nic.academy/","https://rdap.nic.academy/"],school:["https://rdap.nic.school/","https://rdap.nic.school/"],travel:["https://rdap.nic.travel/","https://rdap.nic.travel/"],hotel:["https://rdap.nic.hotel/","https://rdap.nic.hotel/"],restaurant:["https://rdap.nic.restaurant/","https://rdap.nic.restaurant/"]};function M(o){let r=o.toLowerCase().split(".");return r[r.length-1]}async function Z(){let o=Date.now();if($&&o-F<864e5)return $;try{let r=new AbortController,a=setTimeout(()=>r.abort(),1e4),u=await fetch("https://data.iana.org/rdap/dns.json",{headers:{Accept:"application/json","User-Agent":"Domain-Search-Platform/1.0"},signal:r.signal});if(clearTimeout(a),!u.ok)throw Error(`Failed to fetch RDAP bootstrap: ${u.status}`);let p=await u.json();return $=p,F=o,p}catch(r){return console.error("Failed to fetch IANA RDAP bootstrap:",r),null}}async function J(o){let r=M(o),a=[];try{let p=await Z();if(p&&p.services)for(let j of p.services){let[x,O]=j;if(x.includes(r)&&O&&O.length>0){O.forEach(y=>{let U=y.endsWith("/")?y:y+"/";a.push(U)});break}}}catch(p){console.error("Error getting RDAP server from bootstrap:",p)}let u=B[r];if(u&&u.forEach(p=>{a.includes(p)||a.push(p)}),a.length===0){let p=[`https://rdap.nic.${r}/`,`https://rdap.${r}/`,`https://rdap.registry.${r}/`,`https://whois.nic.${r}/rdap/`,`https://rdap-${r}.nic/`,`https://tld-rdap.${r}/`,`https://rdap.${r}.registry/`,`https://rdap.centralnic.com/${r}/`,"https://rdap.identitydigital.services/rdap/",`https://rdap.afilias.info/rdap/${r}/`];a.push(...p)}return a}async function Q(o){let r=M(o),a=q.get(r),u=H.get(r)||0;if((!a||Date.now()-u>216e5)&&(a=await J(o)),!a||a.length===0)throw Error(`No RDAP server found for domain: ${o}`);let p=a.slice(0,3),j=p.map(async x=>{let O=`${x}domain/${o}`;try{let y=new AbortController,U=setTimeout(()=>y.abort(),3e3),I=await fetch(O,{headers:{Accept:"application/rdap+json","User-Agent":"Domain-Search-Platform/1.0"},signal:y.signal});if(clearTimeout(U),!I.ok){if(I.status===404)return{available:!0,reason:"Domain not found in registry",server:x,success:!0};throw I.status===429?Error(`Rate limited on ${x}`):Error(`Server ${x} returned ${I.status}`)}let W=await I.json();if(!W.objectClassName||W.objectClassName!=="domain")throw Error(`Invalid RDAP response from ${x}`);return{available:!1,rdapData:W,server:x,success:!0}}catch(y){throw Error(`${x}: ${y instanceof Error?y.message:"Unknown error"}`)}});try{let x=(await Promise.allSettled(j)).find(y=>y.status==="fulfilled"&&y.value.success);if(!x)throw Error("All servers failed");let O=x.value;if(O.success){let y=[O.server,...p.filter(U=>U!==O.server)];q.set(r,y),H.set(r,Date.now())}return O}catch{return{available:null,error:"All RDAP servers failed",fallback:!0}}}async function V(o){try{let r=new AbortController,a=setTimeout(()=>r.abort(),2e3),u=fetch(`https://${o}`,{method:"HEAD",signal:r.signal,redirect:"follow"}),p=fetch(`http://${o}`,{method:"HEAD",signal:r.signal,redirect:"follow"});try{let j=(await Promise.allSettled([u,p])).some(x=>x.status==="fulfilled");return clearTimeout(a),j}catch{return clearTimeout(a),!1}}catch{return!1}}async function z(o){if(await V(o))return{domain:o,is_available:!1,registrar:"Unknown (RDAP query failed)",registrar_iana_id:null,registrar_whois_server:null,registrar_url:null,created_date:null,updated_date:null,expiry_date:null,status:["RDAP query failed - verified via HTTP"],name_servers:[],dnssec:"unknown",registrar_abuse_contact_email:null,registrar_abuse_contact_phone:null,registry_domain_id:null,last_update_of_whois_database:new Date().toISOString(),fallback_method:"HTTP verification"};let[r]=o.split("."),a=r.length<=4||/^(test|demo|example|www|mail|app|api|admin)$/.test(r.toLowerCase());return{domain:o,is_available:!a,registrar:a?"Unknown (RDAP query failed)":null,created_date:null,expiry_date:null,status:a?["RDAP query failed - heuristic guess"]:[],name_servers:[],fallback_method:"Heuristic guess"}}async function Y(o,{params:r}){let a=r.domain.toLowerCase().trim();if(!a||!a.includes(".")||a.startsWith(".")||a.endsWith("."))return k.xk.json({error:"Invalid domain format"},{status:400});try{let u,p=E.ZU.domain(a),j=E.F_.get(p);if(j)return k.xk.json(j);let x=await Q(a),O=(u=x.available===!0?{domain:a,is_available:!0,registrar:null,created_date:null,expiry_date:null,status:[],name_servers:[]}:x.available===!1&&x.rdapData?function(y,U){let I=y.events||[],W=y.entities||[],_t=y.status||[],dt=y.nameservers||[],T=W.find(g=>g.roles&&g.roles.includes("registrar")),ut=I.find(g=>g.eventAction==="registration"),ht=I.find(g=>g.eventAction==="last changed"),lt=I.find(g=>g.eventAction==="expiration"),ft=dt.map(g=>g.ldhName||g.unicodeName).filter(Boolean),rt="Unknown Registrar",at=null,st=null,it=null;if(T){if(T.vcardArray&&T.vcardArray[1]){let g=T.vcardArray[1].find(L=>L[0]==="fn");g&&g[3]&&(rt=g[3])}if(T.publicIds){let g=T.publicIds.find(L=>L.type==="IANA Registrar ID");g&&(at=g.identifier)}if(T.vcardArray&&T.vcardArray[1]){let g=T.vcardArray[1].find(tt=>tt[0]==="email"),L=T.vcardArray[1].find(tt=>tt[0]==="tel");g&&g[3]&&(st=g[3]),L&&L[3]&&(it=L[3])}}return{domain:U,is_available:!1,registrar:rt,registrar_iana_id:at,registrar_whois_server:y.port43||null,registrar_url:T?.links?.find(g=>g.rel==="related")?.href||null,created_date:ut?.eventDate||null,updated_date:ht?.eventDate||null,expiry_date:lt?.eventDate||null,status:_t,name_servers:ft,dnssec:y.secureDNS?.delegationSigned?"signedDelegation":"unsigned",registrar_abuse_contact_email:st,registrar_abuse_contact_phone:it,registry_domain_id:y.handle||null,last_update_of_whois_database:new Date().toISOString()}}(x.rdapData,a):{...await z(a),fallback_reason:x.error}).is_available?6e5:18e5;return E.F_.set(p,u,O),k.xk.json(u)}catch(u){console.error(`\u274C Error processing domain ${a}:`,u);let p=await z(a);return k.xk.json({...p,error:u instanceof Error?u.message:"Unknown error occurred"})}}let C=new b.AppRouteRouteModule({definition:{kind:w.x.APP_ROUTE,page:"/api/domain/[domain]/route",pathname:"/api/domain/[domain]",filename:"route",bundlePath:"app/api/domain/[domain]/route"},resolvedPagePath:"/mnt/d/Demo/yuming/src/app/api/domain/[domain]/route.ts",nextConfigOutput:"",userland:e}),{requestAsyncStorage:G,staticGenerationAsyncStorage:D,serverHooks:t}=C,d="/api/domain/[domain]/route";function c(){return(0,f.XH)({serverHooks:t,staticGenerationAsyncStorage:D})}let i=_,S=n.a.wrap(C)},8754:Nt},A=>{var N=e=>A(A.s=e);A.O(0,[294,809],()=>N(5074));var s=A.O();(v._ENTRIES=typeof v._ENTRIES>"u"?{}:v._ENTRIES)["middleware_app/api/domain/[domain]/route"]=s}]),function(){let A={exports:{},loaded:!1};return function(s,e){var _=Object.create,n=Object.defineProperty,b=Object.getOwnPropertyDescriptor,w=Object.getOwnPropertyNames,f=Object.getPrototypeOf,k=Object.prototype.hasOwnProperty,E=t=>n(t,"__esModule",{value:!0}),R=(t,d)=>{E(t);for(var c in d)n(t,c,{get:d[c],enumerable:!0})},$=(t,d,c)=>{if(d&&typeof d=="object"||typeof d=="function")for(let i of w(d))!k.call(t,i)&&i!=="default"&&n(t,i,{get:()=>d[i],enumerable:!(c=b(d,i))||c.enumerable});return t},F=t=>$(E(n(t!=null?_(f(t)):{},"default",t&&t.__esModule&&"default"in t?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t);R(e,{default:()=>Y});var q=F((ot(),vt(X))),H="@next/request-context",B=Symbol.for(H),M=Symbol.for("internal.storage");function Z(){let t=v;if(!t[B]){let d=new q.AsyncLocalStorage,c={get:()=>d.getStore(),[M]:d};t[B]=c}return t[B]}var J=Z();function Q(t,d){return J[M].run(t,d)}function V(t){let d={};return t&&t.forEach((c,i)=>{d[i]=c,i.toLowerCase()==="set-cookie"&&(d[i]=z(c))}),d}function z(t){let d=[],c=0,i,S,o,r,a;function u(){for(;c<t.length&&/\s/.test(t.charAt(c));)c+=1;return c<t.length}function p(){return S=t.charAt(c),S!=="="&&S!==";"&&S!==","}for(;c<t.length;){for(i=c,a=!1;u();)if(S=t.charAt(c),S===","){for(o=c,c+=1,u(),r=c;c<t.length&&p();)c+=1;c<t.length&&t.charAt(c)==="="?(a=!0,c=r,d.push(t.substring(i,o)),i=c):c=o+1}else c+=1;(!a||c>=t.length)&&d.push(t.substring(i,t.length))}return d}function Y(t){let d=t.staticRoutes.map(i=>({regexp:new RegExp(i.namedRegex),page:i.page})),c=t.dynamicRoutes?.map(i=>({regexp:new RegExp(i.namedRegex),page:i.page}))||[];return async function(i,S){let o=new URL(i.url).pathname,r={};if(t.nextConfig?.basePath&&o.startsWith(t.nextConfig.basePath)&&(o=o.replace(t.nextConfig.basePath,"")||"/"),t.nextConfig?.i18n)for(let u of t.nextConfig.i18n.locales){let p=new RegExp(`^/${u}($|/)`,"i");if(o.match(p)){o=o.replace(p,"/")||"/";break}}for(let u of d)if(u.regexp.exec(o)){r.name=u.page;break}if(!r.name){let u=G(o);for(let p of c||[]){if(u&&!G(p.page))continue;let j=p.regexp.exec(o);if(j){r={name:p.page,params:j.groups};break}}}let a=await Q({waitUntil:S.waitUntil},()=>v._ENTRIES[`middleware_${t.name}`].default.call({},{request:{url:i.url,method:i.method,headers:V(i.headers),ip:C(i.headers,D.Ip),geo:{city:C(i.headers,D.City,!0),country:C(i.headers,D.Country,!0),latitude:C(i.headers,D.Latitude),longitude:C(i.headers,D.Longitude),region:C(i.headers,D.Region,!0)},nextConfig:t.nextConfig,page:r,body:i.body}}));return a.waitUntil&&S.waitUntil(a.waitUntil),a.response}}function C(t,d,c=!1){let i=t.get(d)||void 0;return c&&i?decodeURIComponent(i):i}function G(t){return t==="/api"||t.startsWith("/api/")}var D;(function(t){t.City="x-vercel-ip-city",t.Country="x-vercel-ip-country",t.Ip="x-real-ip",t.Latitude="x-vercel-ip-latitude",t.Longitude="x-vercel-ip-longitude",t.Region="x-vercel-ip-country-region"})(D||(D={}))}(A,A.exports),A.exports}.call({}).default({name:"app/api/domain/[domain]/route",staticRoutes:[{page:"/",regex:"^/(?:/)?$",routeKeys:{},namedRegex:"^/(?:/)?$"},{page:"/_not-found",regex:"^/_not\\-found(?:/)?$",routeKeys:{},namedRegex:"^/_not\\-found(?:/)?$"},{page:"/about",regex:"^/about(?:/)?$",routeKeys:{},namedRegex:"^/about(?:/)?$"},{page:"/privacy",regex:"^/privacy(?:/)?$",routeKeys:{},namedRegex:"^/privacy(?:/)?$"},{page:"/search",regex:"^/search(?:/)?$",routeKeys:{},namedRegex:"^/search(?:/)?$"},{page:"/sitemap.xml",regex:"^/sitemap\\.xml(?:/)?$",routeKeys:{},namedRegex:"^/sitemap\\.xml(?:/)?$"},{page:"/terms",regex:"^/terms(?:/)?$",routeKeys:{},namedRegex:"^/terms(?:/)?$"}],dynamicRoutes:[{page:"/api/domain/[domain]",regex:"^/api/domain/([^/]+?)(?:/)?$",routeKeys:{nxtPdomain:"nxtPdomain"},namedRegex:"^/api/domain/(?<nxtPdomain>[^/]+?)(?:/)?$"},{page:"/domain/[domain]",regex:"^/domain/([^/]+?)(?:/)?$",routeKeys:{nxtPdomain:"nxtPdomain"},namedRegex:"^/domain/(?<nxtPdomain>[^/]+?)(?:/)?$"}],nextConfig:{basePath:""}})))(P,P,P);export{Se as default};
