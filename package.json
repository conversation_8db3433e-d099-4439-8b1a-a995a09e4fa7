{"name": "domain-search", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "pages:build": "npx @cloudflare/next-on-pages", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.12", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.1.1", "framer-motion": "^10.0.0", "fuse.js": "^7.1.0", "lucide-react": "^0.263.0", "next": "^14.0.0", "next-themes": "^0.2.1", "postcss": "^8.4.0", "react": "^18.0.0", "react-dom": "^18.0.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "zustand": "^4.4.0"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@cloudflare/workers-types": "^4.20250715.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}, "engines": {"node": ">=20.18.1", "npm": ">=8.0.0"}}