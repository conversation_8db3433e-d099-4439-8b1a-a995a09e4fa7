var a={},z=(N,b,v)=>(a.__chunk_6316=(l,s,t)=>{"use strict";t.d(s,{Z:()=>e});let e=(0,t(8622).Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},a.__chunk_4377=(l,s,t)=>{"use strict";t.d(s,{Z:()=>e});let e=(0,t(8622).Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},a.__chunk_7964=(l,s,t)=>{"use strict";t.d(s,{Z:()=>e});let e=(0,t(8622).Z)("Languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]])},a.__chunk_8489=(l,s,t)=>{"use strict";t.d(s,{T:()=>i});var e=t(926);t(9220);var n=t(6316),o=t(4377),c=t(4164),h=t(4687);function i(){let{setTheme:d,theme:p}=(0,c.F)();return(0,e.jsxs)(h.z,{variant:"outline",size:"icon",onClick:()=>d(p==="light"?"dark":"light"),children:[(0,e.jsx)(n.Z,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,e.jsx)(o.Z,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,e.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}},a.__chunk_7410=(l,s,t)=>{"use strict";t.d(s,{W:()=>d});var e=t(926),n=t(7964),o=t(3531),c=t(4687),h=t(6516),i=t(9220);function d({currentLocale:p}){let{t:k,locale:x,switchLocale:g,isClient:_}=(0,h.T)(),[m,u]=(0,i.useState)(!1),[y,w]=(0,i.useState)(!1);if(!y)return(0,e.jsxs)(c.z,{variant:"outline",size:"icon",className:"relative",children:[(0,e.jsx)(n.Z,{className:"h-[1.2rem] w-[1.2rem]"}),(0,e.jsx)("span",{className:"absolute -bottom-1 -right-1 text-[10px] font-bold bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center",children:"\u4E2D"})]});let j={en:"English","zh-CN":"\u7B80\u4F53\u4E2D\u6587"},f=r=>{g(r),u(!1)};return(0,e.jsxs)("div",{className:"relative",children:[(0,e.jsxs)(c.z,{variant:"outline",size:"icon",className:"relative",onClick:()=>u(!m),children:[(0,e.jsx)(n.Z,{className:"h-[1.2rem] w-[1.2rem]"}),(0,e.jsx)("span",{className:"sr-only",children:k("header.switchLanguage")}),(0,e.jsx)("span",{className:"absolute -bottom-1 -right-1 text-[10px] font-bold bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center",children:x==="zh-CN"?"\u4E2D":"EN"})]}),m&&(0,e.jsx)("div",{className:"absolute right-0 top-full mt-2 w-48 bg-background border rounded-lg shadow-lg z-[9999] overflow-hidden",children:["zh-CN","en"].map(r=>(0,e.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-2 hover:bg-accent cursor-pointer transition-colors",onClick:()=>f(r),children:[(0,e.jsx)(o.Z,{className:"h-4 w-4"}),(0,e.jsx)("span",{className:"flex-1",children:j[r]}),r===x&&(0,e.jsx)("span",{className:"text-primary font-bold",children:"\u2713"})]},r))}),m&&(0,e.jsx)("div",{className:"fixed inset-0 z-[9998]",onClick:()=>u(!1)})]})}},a);export{z as __getNamedExports};
