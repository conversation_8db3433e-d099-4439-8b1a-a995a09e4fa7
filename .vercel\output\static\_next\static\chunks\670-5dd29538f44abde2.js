"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[670],{5523:function(t,e,r){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"RouterContext",{enumerable:!0,get:function(){return i}});let i=r(7043)._(r(2265)).default.createContext(null)},8482:function(t,e,r){r.d(e,{g7:function(){return a},Z8:function(){return s}});var i=r(2265);function n(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}var o=r(7437);function s(t){let e=function(t){let e=i.forwardRef((t,e)=>{let{children:r,...o}=t;if(i.isValidElement(r)){let t,s;let a=(t=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?r.ref:(t=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?r.props.ref:r.props.ref||r.ref,l=function(t,e){let r={...e};for(let i in e){let n=t[i],o=e[i];/^on[A-Z]/.test(i)?n&&o?r[i]=(...t)=>{let e=o(...t);return n(...t),e}:n&&(r[i]=n):"style"===i?r[i]={...n,...o}:"className"===i&&(r[i]=[n,o].filter(Boolean).join(" "))}return{...t,...r}}(o,r.props);return r.type!==i.Fragment&&(l.ref=e?function(...t){return e=>{let r=!1,i=t.map(t=>{let i=n(t,e);return r||"function"!=typeof i||(r=!0),i});if(r)return()=>{for(let e=0;e<i.length;e++){let r=i[e];"function"==typeof r?r():n(t[e],null)}}}}(e,a):a),i.cloneElement(r,l)}return i.Children.count(r)>1?i.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),r=i.forwardRef((t,r)=>{let{children:n,...s}=t,a=i.Children.toArray(n),l=a.find(u);if(l){let t=l.props.children,n=a.map(e=>e!==l?e:i.Children.count(t)>1?i.Children.only(null):i.isValidElement(t)?t.props.children:null);return(0,o.jsx)(e,{...s,ref:r,children:i.isValidElement(t)?i.cloneElement(t,void 0,n):null})}return(0,o.jsx)(e,{...s,ref:r,children:n})});return r.displayName=`${t}.Slot`,r}var a=s("Slot"),l=Symbol("radix.slottable");function u(t){return i.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===l}},535:function(t,e,r){r.d(e,{j:function(){return s}});var i=r(1994);let n=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,o=i.W,s=(t,e)=>r=>{var i;if((null==e?void 0:e.variants)==null)return o(t,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:a}=e,l=Object.keys(s).map(t=>{let e=null==r?void 0:r[t],i=null==a?void 0:a[t];if(null===e)return null;let o=n(e)||n(i);return s[t][o]}),u=r&&Object.entries(r).reduce((t,e)=>{let[r,i]=e;return void 0===i||(t[r]=i),t},{});return o(t,l,null==e?void 0:null===(i=e.compoundVariants)||void 0===i?void 0:i.reduce((t,e)=>{let{class:r,className:i,...n}=e;return Object.entries(n).every(t=>{let[e,r]=t;return Array.isArray(r)?r.includes({...a,...u}[e]):({...a,...u})[e]===r})?[...t,r,i]:t},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},1994:function(t,e,r){r.d(e,{W:function(){return i}});function i(){for(var t,e,r=0,i="",n=arguments.length;r<n;r++)(t=arguments[r])&&(e=function t(e){var r,i,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e){if(Array.isArray(e)){var o=e.length;for(r=0;r<o;r++)e[r]&&(i=t(e[r]))&&(n&&(n+=" "),n+=i)}else for(i in e)e[i]&&(n&&(n+=" "),n+=i)}return n}(t))&&(i&&(i+=" "),i+=e);return i}},8881:function(t,e,r){r.d(e,{p:function(){return i}});let i=(0,r(2265).createContext)({})},4252:function(t,e,r){r.d(e,{O:function(){return i}});let i=(0,r(2265).createContext)(null)},8345:function(t,e,r){r.d(e,{Pn:function(){return a},Wi:function(){return s},frameData:function(){return l},S6:function(){return u}});var i=r(4439);class n{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){let e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}let o=["prepare","read","update","preRender","render","postRender"],{schedule:s,cancel:a,state:l,steps:u}=function(t,e){let r=!1,i=!0,s={delta:0,timestamp:0,isProcessing:!1},a=o.reduce((t,e)=>(t[e]=function(t){let e=new n,r=new n,i=0,o=!1,s=!1,a=new WeakSet,l={schedule:(t,n=!1,s=!1)=>{let l=s&&o,u=l?e:r;return n&&a.add(t),u.add(t)&&l&&o&&(i=e.order.length),t},cancel:t=>{r.remove(t),a.delete(t)},process:n=>{if(o){s=!0;return}if(o=!0,[e,r]=[r,e],r.clear(),i=e.order.length)for(let r=0;r<i;r++){let i=e.order[r];i(n),a.has(i)&&(l.schedule(i),t())}o=!1,s&&(s=!1,l.process(n))}};return l}(()=>r=!0),t),{}),l=t=>a[t].process(s),u=()=>{let n=performance.now();r=!1,s.delta=i?1e3/60:Math.max(Math.min(n-s.timestamp,40),1),s.timestamp=n,s.isProcessing=!0,o.forEach(l),s.isProcessing=!1,r&&e&&(i=!1,t(u))},h=()=>{r=!0,i=!0,s.isProcessing||t(u)};return{schedule:o.reduce((t,e)=>{let i=a[e];return t[e]=(t,e=!1,n=!1)=>(r||h(),i.schedule(t,e,n)),t},{}),cancel:t=>o.forEach(e=>a[e].cancel(t)),state:s,steps:a}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:i.Z,!0)},429:function(t,e,r){let i;r.d(e,{E:function(){return nH}});var n,o,s=r(2265);let a=(0,s.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),l=(0,s.createContext)({});var u=r(4252),h=r(1534);let c=(0,s.createContext)({strict:!1}),d=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),p="data-"+d("framerAppearId");function m(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function f(t){return"string"==typeof t||Array.isArray(t)}function g(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}let v=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],y=["initial",...v];function b(t){return g(t.animate)||y.some(e=>f(t[e]))}function x(t){return!!(b(t)||t.variants)}function w(t){return Array.isArray(t)?t.join(" "):t}let P={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},A={};for(let t in P)A[t]={isEnabled:e=>P[t].some(t=>!!e[t])};var T=r(4563),S=r(8881);let k=(0,s.createContext)({}),V=Symbol.for("motionComponentSymbol"),C=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function E(t){if("string"!=typeof t||t.includes("-"));else if(C.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}let M={},D=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],j=new Set(D);function R(t,{layout:e,layoutId:r}){return j.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!M[t]||"opacity"===t)}let L=t=>!!(t&&t.getVelocity),F={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},B=D.length,O=t=>e=>"string"==typeof e&&e.startsWith(t),W=O("--"),I=O("var(--"),z=(t,e)=>e&&"number"==typeof t?e.transform(t):t,U=(t,e,r)=>Math.min(Math.max(r,t),e),N={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},$={...N,transform:t=>U(0,1,t)},Z={...N,default:1},H=t=>Math.round(1e5*t)/1e5,G=/(-)?([\d]*\.?[\d])+/g,Y=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,X=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function q(t){return"string"==typeof t}let _=t=>({test:e=>q(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),K=_("deg"),J=_("%"),Q=_("px"),tt=_("vh"),te=_("vw"),tr={...J,parse:t=>J.parse(t)/100,transform:t=>J.transform(100*t)},ti={...N,transform:Math.round},tn={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,size:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,rotate:K,rotateX:K,rotateY:K,rotateZ:K,scale:Z,scaleX:Z,scaleY:Z,scaleZ:Z,skew:K,skewX:K,skewY:K,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:$,originX:tr,originY:tr,originZ:Q,zIndex:ti,fillOpacity:$,strokeOpacity:$,numOctaves:ti};function to(t,e,r,i){let{style:n,vars:o,transform:s,transformOrigin:a}=t,l=!1,u=!1,h=!0;for(let t in e){let r=e[t];if(W(t)){o[t]=r;continue}let i=tn[t],c=z(r,i);if(j.has(t)){if(l=!0,s[t]=c,!h)continue;r!==(i.default||0)&&(h=!1)}else t.startsWith("origin")?(u=!0,a[t]=c):n[t]=c}if(!e.transform&&(l||i?n.transform=function(t,{enableHardwareAcceleration:e=!0,allowTransformNone:r=!0},i,n){let o="";for(let e=0;e<B;e++){let r=D[e];if(void 0!==t[r]){let e=F[r]||r;o+=`${e}(${t[r]}) `}}return e&&!t.z&&(o+="translateZ(0)"),o=o.trim(),n?o=n(t,i?"":o):r&&i&&(o="none"),o}(t.transform,r,h,i):n.transform&&(n.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:r=0}=a;n.transformOrigin=`${t} ${e} ${r}`}}let ts=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ta(t,e,r){for(let i in e)L(e[i])||R(i,r)||(t[i]=e[i])}let tl=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tu(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||tl.has(t)}let th=t=>!tu(t);try{(n=require("@emotion/is-prop-valid").default)&&(th=t=>t.startsWith("on")?!tu(t):n(t))}catch(t){}function tc(t,e,r){return"string"==typeof t?t:Q.transform(e+r*t)}let td={offset:"stroke-dashoffset",array:"stroke-dasharray"},tp={offset:"strokeDashoffset",array:"strokeDasharray"};function tm(t,{attrX:e,attrY:r,attrScale:i,originX:n,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},h,c,d){if(to(t,u,h,d),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:p,style:m,dimensions:f}=t;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==n||void 0!==o||m.transform)&&(m.transformOrigin=function(t,e,r){let i=tc(e,t.x,t.width),n=tc(r,t.y,t.height);return`${i} ${n}`}(f,void 0!==n?n:.5,void 0!==o?o:.5)),void 0!==e&&(p.x=e),void 0!==r&&(p.y=r),void 0!==i&&(p.scale=i),void 0!==s&&function(t,e,r=1,i=0,n=!0){t.pathLength=1;let o=n?td:tp;t[o.offset]=Q.transform(-i);let s=Q.transform(e),a=Q.transform(r);t[o.array]=`${s} ${a}`}(p,s,a,l,!1)}let tf=()=>({...ts(),attrs:{}}),tg=t=>"string"==typeof t&&"svg"===t.toLowerCase();function tv(t,{style:e,vars:r},i,n){for(let o in Object.assign(t.style,e,n&&n.getProjectionStyles(i)),r)t.style.setProperty(o,r[o])}let ty=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tb(t,e,r,i){for(let r in tv(t,e,void 0,i),e.attrs)t.setAttribute(ty.has(r)?r:d(r),e.attrs[r])}function tx(t,e){let{style:r}=t,i={};for(let n in r)(L(r[n])||e.style&&L(e.style[n])||R(n,t))&&(i[n]=r[n]);return i}function tw(t,e){let r=tx(t,e);for(let i in t)(L(t[i])||L(e[i]))&&(r[-1!==D.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}function tP(t,e,r,i={},n={}){return"function"==typeof e&&(e=e(void 0!==r?r:t.custom,i,n)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==r?r:t.custom,i,n)),e}var tA=r(3576);let tT=t=>Array.isArray(t),tS=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),tk=t=>tT(t)?t[t.length-1]||0:t;function tV(t){let e=L(t)?t.get():t;return tS(e)?e.toValue():e}let tC=t=>(e,r)=>{let i=(0,s.useContext)(l),n=(0,s.useContext)(u.O),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:r},i,n,o){let s={latestValues:function(t,e,r,i){let n={},o=i(t,{});for(let t in o)n[t]=tV(o[t]);let{initial:s,animate:a}=t,l=b(t),u=x(t);e&&u&&!l&&!1!==t.inherit&&(void 0===s&&(s=e.initial),void 0===a&&(a=e.animate));let h=!!r&&!1===r.initial,c=(h=h||!1===s)?a:s;return c&&"boolean"!=typeof c&&!g(c)&&(Array.isArray(c)?c:[c]).forEach(e=>{let r=tP(t,e);if(!r)return;let{transitionEnd:i,transition:o,...s}=r;for(let t in s){let e=s[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let t in i)n[t]=i[t]}),n}(i,n,o,t),renderState:e()};return r&&(s.mount=t=>r(i,t,s)),s})(t,e,i,n);return r?o():(0,tA.h)(o)};var tE=r(8345);let tM={useVisualState:tC({scrapeMotionValuesFromProps:tw,createRenderState:tf,onMount:(t,e,{renderState:r,latestValues:i})=>{tE.Wi.read(()=>{try{r.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){r.dimensions={x:0,y:0,width:0,height:0}}}),tE.Wi.render(()=>{tm(r,i,{enableHardwareAcceleration:!1},tg(e.tagName),t.transformTemplate),tb(e,r)})}})},tD={useVisualState:tC({scrapeMotionValuesFromProps:tx,createRenderState:ts})};function tj(t,e,r,i={passive:!0}){return t.addEventListener(e,r,i),()=>t.removeEventListener(e,r)}let tR=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tL(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}let tF=t=>e=>tR(e)&&t(e,tL(e));function tB(t,e,r,i){return tj(t,e,tF(r),i)}let tO=(t,e)=>r=>e(t(r)),tW=(...t)=>t.reduce(tO);function tI(t){let e=null;return()=>null===e&&(e=t,()=>{e=null})}let tz=tI("dragHorizontal"),tU=tI("dragVertical");function tN(t){let e=!1;if("y"===t)e=tU();else if("x"===t)e=tz();else{let t=tz(),r=tU();t&&r?e=()=>{t(),r()}:(t&&t(),r&&r())}return e}function t$(){let t=tN(!0);return!t||(t(),!1)}class tZ{constructor(t){this.isMounted=!1,this.node=t}update(){}}function tH(t,e){let r="onHover"+(e?"Start":"End");return tB(t.current,"pointer"+(e?"enter":"leave"),(i,n)=>{if("touch"===i.pointerType||t$())return;let o=t.getProps();t.animationState&&o.whileHover&&t.animationState.setActive("whileHover",e),o[r]&&tE.Wi.update(()=>o[r](i,n))},{passive:!t.getProps()[r]})}class tG extends tZ{mount(){this.unmount=tW(tH(this.node,!0),tH(this.node,!1))}unmount(){}}class tY extends tZ{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tW(tj(this.node.current,"focus",()=>this.onFocus()),tj(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let tX=(t,e)=>!!e&&(t===e||tX(t,e.parentElement));var tq=r(4439);function t_(t,e){if(!e)return;let r=new PointerEvent("pointer"+t);e(r,tL(r))}class tK extends tZ{constructor(){super(...arguments),this.removeStartListeners=tq.Z,this.removeEndListeners=tq.Z,this.removeAccessibleListeners=tq.Z,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),i=tB(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:i,globalTapTarget:n}=this.node.getProps();tE.Wi.update(()=>{n||tX(this.node.current,t.target)?r&&r(t,e):i&&i(t,e)})},{passive:!(r.onTap||r.onPointerUp)}),n=tB(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=tW(i,n),this.startPress(t,e)},this.startAccessiblePress=()=>{let t=tj(this.node.current,"keydown",t=>{"Enter"!==t.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=tj(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&t_("up",(t,e)=>{let{onTap:r}=this.node.getProps();r&&tE.Wi.update(()=>r(t,e))})}),t_("down",(t,e)=>{this.startPress(t,e)}))}),e=tj(this.node.current,"blur",()=>{this.isPressing&&t_("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=tW(t,e)}}startPress(t,e){this.isPressing=!0;let{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&tE.Wi.update(()=>r(t,e))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!t$()}cancelPress(t,e){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&tE.Wi.update(()=>r(t,e))}mount(){let t=this.node.getProps(),e=tB(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=tj(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=tW(e,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let tJ=new WeakMap,tQ=new WeakMap,t0=t=>{let e=tJ.get(t.target);e&&e(t)},t1=t=>{t.forEach(t0)},t5={some:0,all:1};class t2 extends tZ{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:r,amount:i="some",once:n}=t,o={root:e?e.current:void 0,rootMargin:r,threshold:"number"==typeof i?i:t5[i]};return function(t,e,r){let i=function({root:t,...e}){let r=t||document;tQ.has(r)||tQ.set(r,{});let i=tQ.get(r),n=JSON.stringify(e);return i[n]||(i[n]=new IntersectionObserver(t1,{root:t,...e})),i[n]}(e);return tJ.set(t,r),i.observe(t),()=>{tJ.delete(t),i.unobserve(t)}}(this.node.current,o,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:r,onViewportLeave:i}=this.node.getProps(),o=e?r:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return r=>t[r]!==e[r]}(t,e))&&this.startObserver()}unmount(){}}function t3(t,e){if(!Array.isArray(e))return!1;let r=e.length;if(r!==t.length)return!1;for(let i=0;i<r;i++)if(e[i]!==t[i])return!1;return!0}function t4(t,e,r){let i=t.getProps();return tP(i,e,void 0!==r?r:i.custom,function(t){let e={};return t.values.forEach((t,r)=>e[r]=t.get()),e}(t),function(t){let e={};return t.values.forEach((t,r)=>e[r]=t.getVelocity()),e}(t))}var t6=r(3223);let t9=t=>1e3*t,t8=t=>t/1e3,t7={current:!1},et=t=>Array.isArray(t)&&"number"==typeof t[0],ee=([t,e,r,i])=>`cubic-bezier(${t}, ${e}, ${r}, ${i})`,er={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ee([0,.65,.55,1]),circOut:ee([.55,0,1,.45]),backIn:ee([.31,.01,.66,-.59]),backOut:ee([.33,1.53,.69,.99])},ei=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t;function en(t,e,r,i){if(t===e&&r===i)return tq.Z;let n=e=>(function(t,e,r,i,n){let o,s;let a=0;do(o=ei(s=e+(r-e)/2,i,n)-t)>0?r=s:e=s;while(Math.abs(o)>1e-7&&++a<12);return s})(e,0,1,t,r);return t=>0===t||1===t?t:ei(n(t),e,i)}let eo=en(.42,0,1,1),es=en(0,0,.58,1),ea=en(.42,0,.58,1),el=t=>Array.isArray(t)&&"number"!=typeof t[0],eu=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,eh=t=>e=>1-t(1-e),ec=t=>1-Math.sin(Math.acos(t)),ed=eh(ec),ep=eu(ec),em=en(.33,1.53,.69,.99),ef=eh(em),eg=eu(ef),ev={linear:tq.Z,easeIn:eo,easeInOut:ea,easeOut:es,circIn:ec,circInOut:ep,circOut:ed,backIn:ef,backInOut:eg,backOut:em,anticipate:t=>(t*=2)<1?.5*ef(t):.5*(2-Math.pow(2,-10*(t-1)))},ey=t=>{if(Array.isArray(t)){(0,t6.k)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,r,i,n]=t;return en(e,r,i,n)}return"string"==typeof t?((0,t6.k)(void 0!==ev[t],`Invalid easing type '${t}'`),ev[t]):t},eb=(t,e)=>r=>!!(q(r)&&X.test(r)&&r.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(r,e)),ex=(t,e,r)=>i=>{if(!q(i))return i;let[n,o,s,a]=i.match(G);return{[t]:parseFloat(n),[e]:parseFloat(o),[r]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},ew=t=>U(0,255,t),eP={...N,transform:t=>Math.round(ew(t))},eA={test:eb("rgb","red"),parse:ex("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:i=1})=>"rgba("+eP.transform(t)+", "+eP.transform(e)+", "+eP.transform(r)+", "+H($.transform(i))+")"},eT={test:eb("#"),parse:function(t){let e="",r="",i="",n="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),i=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),i=t.substring(3,4),n=t.substring(4,5),e+=e,r+=r,i+=i,n+=n),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:eA.transform},eS={test:eb("hsl","hue"),parse:ex("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:i=1})=>"hsla("+Math.round(t)+", "+J.transform(H(e))+", "+J.transform(H(r))+", "+H($.transform(i))+")"},ek={test:t=>eA.test(t)||eT.test(t)||eS.test(t),parse:t=>eA.test(t)?eA.parse(t):eS.test(t)?eS.parse(t):eT.parse(t),transform:t=>q(t)?t:t.hasOwnProperty("red")?eA.transform(t):eS.transform(t)},eV=(t,e,r)=>-r*t+r*e+t;function eC(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}let eE=(t,e,r)=>{let i=t*t;return Math.sqrt(Math.max(0,r*(e*e-i)+i))},eM=[eT,eA,eS],eD=t=>eM.find(e=>e.test(t));function ej(t){let e=eD(t);(0,t6.k)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`);let r=e.parse(t);return e===eS&&(r=function({hue:t,saturation:e,lightness:r,alpha:i}){t/=360,r/=100;let n=0,o=0,s=0;if(e/=100){let i=r<.5?r*(1+e):r+e-r*e,a=2*r-i;n=eC(a,i,t+1/3),o=eC(a,i,t),s=eC(a,i,t-1/3)}else n=o=s=r;return{red:Math.round(255*n),green:Math.round(255*o),blue:Math.round(255*s),alpha:i}}(r)),r}let eR=(t,e)=>{let r=ej(t),i=ej(e),n={...r};return t=>(n.red=eE(r.red,i.red,t),n.green=eE(r.green,i.green,t),n.blue=eE(r.blue,i.blue,t),n.alpha=eV(r.alpha,i.alpha,t),eA.transform(n))},eL={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:tq.Z},eF={regex:Y,countKey:"Colors",token:"${c}",parse:ek.parse},eB={regex:G,countKey:"Numbers",token:"${n}",parse:N.parse};function eO(t,{regex:e,countKey:r,token:i,parse:n}){let o=t.tokenised.match(e);o&&(t["num"+r]=o.length,t.tokenised=t.tokenised.replace(e,i),t.values.push(...o.map(n)))}function eW(t){let e=t.toString(),r={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&eO(r,eL),eO(r,eF),eO(r,eB),r}function eI(t){return eW(t).values}function ez(t){let{values:e,numColors:r,numVars:i,tokenised:n}=eW(t),o=e.length;return t=>{let e=n;for(let n=0;n<o;n++)e=n<i?e.replace(eL.token,t[n]):n<i+r?e.replace(eF.token,ek.transform(t[n])):e.replace(eB.token,H(t[n]));return e}}let eU=t=>"number"==typeof t?0:t,eN={test:function(t){var e,r;return isNaN(t)&&q(t)&&((null===(e=t.match(G))||void 0===e?void 0:e.length)||0)+((null===(r=t.match(Y))||void 0===r?void 0:r.length)||0)>0},parse:eI,createTransformer:ez,getAnimatableNone:function(t){let e=eI(t);return ez(t)(e.map(eU))}},e$=(t,e)=>r=>`${r>0?e:t}`;function eZ(t,e){return"number"==typeof t?r=>eV(t,e,r):ek.test(t)?eR(t,e):t.startsWith("var(")?e$(t,e):eY(t,e)}let eH=(t,e)=>{let r=[...t],i=r.length,n=t.map((t,r)=>eZ(t,e[r]));return t=>{for(let e=0;e<i;e++)r[e]=n[e](t);return r}},eG=(t,e)=>{let r={...t,...e},i={};for(let n in r)void 0!==t[n]&&void 0!==e[n]&&(i[n]=eZ(t[n],e[n]));return t=>{for(let e in i)r[e]=i[e](t);return r}},eY=(t,e)=>{let r=eN.createTransformer(e),i=eW(t),n=eW(e);return i.numVars===n.numVars&&i.numColors===n.numColors&&i.numNumbers>=n.numNumbers?tW(eH(i.values,n.values),r):((0,t6.K)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),e$(t,e))},eX=(t,e,r)=>{let i=e-t;return 0===i?1:(r-t)/i},eq=(t,e)=>r=>eV(t,e,r);function e_(t,e,{clamp:r=!0,ease:i,mixer:n}={}){let o=t.length;if((0,t6.k)(o===e.length,"Both input and output ranges must be the same length"),1===o)return()=>e[0];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());let s=function(t,e,r){let i=[],n=r||function(t){if("number"==typeof t);else if("string"==typeof t)return ek.test(t)?eR:eY;else if(Array.isArray(t))return eH;else if("object"==typeof t)return eG;return eq}(t[0]),o=t.length-1;for(let r=0;r<o;r++){let o=n(t[r],t[r+1]);e&&(o=tW(Array.isArray(e)?e[r]||tq.Z:e,o)),i.push(o)}return i}(e,i,n),a=s.length,l=e=>{let r=0;if(a>1)for(;r<t.length-2&&!(e<t[r+1]);r++);let i=eX(t[r],t[r+1],e);return s[r](i)};return r?e=>l(U(t[0],t[o-1],e)):l}function eK({duration:t=300,keyframes:e,times:r,ease:i="easeInOut"}){let n=el(i)?i.map(ey):ey(i),o={done:!1,value:e[0]},s=e_((r&&r.length===e.length?r:function(t){let e=[0];return function(t,e){let r=t[t.length-1];for(let i=1;i<=e;i++){let n=eX(0,e,i);t.push(eV(r,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||ea).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=s(e),o.done=e>=t,o)}}function eJ(t,e,r){var i,n;let o=Math.max(e-5,0);return i=r-t(o),(n=e-o)?1e3/n*i:0}function eQ(t,e){return t*Math.sqrt(1-e*e)}let e0=["duration","bounce"],e1=["stiffness","damping","mass"];function e5(t,e){return e.some(e=>void 0!==t[e])}function e2({keyframes:t,restDelta:e,restSpeed:r,...i}){let n;let o=t[0],s=t[t.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!e5(t,e1)&&e5(t,e0)){let r=function({duration:t=800,bounce:e=.25,velocity:r=0,mass:i=1}){let n,o;(0,t6.K)(t<=t9(10),"Spring duration must be 10 seconds or less");let s=1-e;s=U(.05,1,s),t=U(.01,10,t8(t)),s<1?(n=e=>{let i=e*s,n=i*t;return .001-(i-r)/eQ(e,s)*Math.exp(-n)},o=e=>{let i=e*s*t,o=Math.pow(s,2)*Math.pow(e,2)*t,a=eQ(Math.pow(e,2),s);return(i*r+r-o)*Math.exp(-i)*(-n(e)+.001>0?-1:1)/a}):(n=e=>-.001+Math.exp(-e*t)*((e-r)*t+1),o=e=>t*t*(r-e)*Math.exp(-e*t));let a=function(t,e,r){let i=r;for(let r=1;r<12;r++)i-=t(i)/e(i);return i}(n,o,5/t);if(t=t9(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{let e=Math.pow(a,2)*i;return{stiffness:e,damping:2*s*Math.sqrt(i*e),duration:t}}}(t);(e={...e,...r,mass:1}).isResolvedFromDuration=!0}return e}({...i,velocity:-t8(i.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*h)),g=s-o,v=t8(Math.sqrt(l/h)),y=5>Math.abs(g);if(r||(r=y?.01:2),e||(e=y?.005:.5),f<1){let t=eQ(v,f);n=e=>s-Math.exp(-f*v*e)*((m+f*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===f)n=t=>s-Math.exp(-v*t)*(g+(m+v*g)*t);else{let t=v*Math.sqrt(f*f-1);n=e=>{let r=Math.exp(-f*v*e),i=Math.min(t*e,300);return s-r*((m+f*v*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}return{calculatedDuration:p&&c||null,next:t=>{let i=n(t);if(p)a.done=t>=c;else{let o=m;0!==t&&(o=f<1?eJ(n,t,i):0);let l=Math.abs(o)<=r,u=Math.abs(s-i)<=e;a.done=l&&u}return a.value=a.done?s:i,a}}}function e3({keyframes:t,velocity:e=0,power:r=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,v=r*e,y=p+v,b=void 0===s?y:s(y);b!==y&&(v=b-p);let x=t=>-v*Math.exp(-t/i),w=t=>b+x(t),P=t=>{let e=x(t),r=w(t);m.done=Math.abs(e)<=u,m.value=m.done?b:r},A=t=>{f(m.value)&&(c=t,d=e2({keyframes:[m.value,g(m.value)],velocity:eJ(w,t,m.value),damping:n,stiffness:o,restDelta:u,restSpeed:h}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,P(t),A(t)),void 0!==c&&t>c)?d.next(t-c):(e||P(t),m)}}}let e4=t=>{let e=({timestamp:e})=>t(e);return{start:()=>tE.Wi.update(e,!0),stop:()=>(0,tE.Pn)(e),now:()=>tE.frameData.isProcessing?tE.frameData.timestamp:performance.now()}};function e6(t){let e=0,r=t.next(e);for(;!r.done&&e<2e4;)e+=50,r=t.next(e);return e>=2e4?1/0:e}let e9={decay:e3,inertia:e3,tween:eK,keyframes:eK,spring:e2};function e8({autoplay:t=!0,delay:e=0,driver:r=e4,keyframes:i,type:n="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:h,onUpdate:c,...d}){let p,m,f,g,v,y=1,b=!1,x=()=>{m=new Promise(t=>{p=t})};x();let w=e9[n]||eK;w!==eK&&"number"!=typeof i[0]&&(g=e_([0,100],i,{clamp:!1}),i=[0,100]);let P=w({...d,keyframes:i});"mirror"===a&&(v=w({...d,keyframes:[...i].reverse(),velocity:-(d.velocity||0)}));let A="idle",T=null,S=null,k=null;null===P.calculatedDuration&&o&&(P.calculatedDuration=e6(P));let{calculatedDuration:V}=P,C=1/0,E=1/0;null!==V&&(E=(C=V+s)*(o+1)-s);let M=0,D=t=>{if(null===S)return;y>0&&(S=Math.min(S,t)),y<0&&(S=Math.min(t-E/y,S));let r=(M=null!==T?T:Math.round(t-S)*y)-e*(y>=0?1:-1),n=y>=0?r<0:r>E;M=Math.max(r,0),"finished"===A&&null===T&&(M=E);let l=M,u=P;if(o){let t=Math.min(M,E)/C,e=Math.floor(t),r=t%1;!r&&t>=1&&(r=1),1===r&&e--,(e=Math.min(e,o+1))%2&&("reverse"===a?(r=1-r,s&&(r-=s/C)):"mirror"===a&&(u=v)),l=U(0,1,r)*C}let h=n?{done:!1,value:i[0]}:u.next(l);g&&(h.value=g(h.value));let{done:d}=h;n||null===V||(d=y>=0?M>=E:M<=0);let p=null===T&&("finished"===A||"running"===A&&d);return c&&c(h.value),p&&L(),h},j=()=>{f&&f.stop(),f=void 0},R=()=>{A="idle",j(),p(),x(),S=k=null},L=()=>{A="finished",h&&h(),j(),p()},F=()=>{if(b)return;f||(f=r(D));let t=f.now();l&&l(),null!==T?S=t-T:S&&"finished"!==A||(S=t),"finished"===A&&x(),k=S,T=null,A="running",f.start()};t&&F();let B={then:(t,e)=>m.then(t,e),get time(){return t8(M)},set time(newTime){M=newTime=t9(newTime),null===T&&f&&0!==y?S=f.now()-newTime/y:T=newTime},get duration(){return t8(null===P.calculatedDuration?e6(P):P.calculatedDuration)},get speed(){return y},set speed(newSpeed){if(newSpeed===y||!f)return;y=newSpeed,B.time=t8(M)},get state(){return A},play:F,pause:()=>{A="paused",T=M},stop:()=>{b=!0,"idle"!==A&&(A="idle",u&&u(),R())},cancel:()=>{null!==k&&D(k),R()},complete:()=>{A="finished"},sample:t=>(S=0,D(t))};return B}let e7=(o=()=>Object.hasOwnProperty.call(Element.prototype,"animate"),()=>(void 0===i&&(i=o()),i)),rt=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),re=(t,e)=>"spring"===e.type||"backgroundColor"===t||!function t(e){return!!(!e||"string"==typeof e&&er[e]||et(e)||Array.isArray(e)&&e.every(t))}(e.ease),rr={type:"spring",stiffness:500,damping:25,restSpeed:10},ri=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),rn={type:"keyframes",duration:.8},ro={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rs=(t,{keyframes:e})=>e.length>2?rn:j.has(t)?t.startsWith("scale")?ri(e[1]):rr:ro,ra=(t,e)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eN.test(e)||"0"===e)&&!e.startsWith("url(")),rl=new Set(["brightness","contrast","saturate","opacity"]);function ru(t){let[e,r]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[i]=r.match(G)||[];if(!i)return t;let n=r.replace(i,""),o=rl.has(e)?1:0;return i!==r&&(o*=100),e+"("+o+n+")"}let rh=/([a-z-]*)\(.*?\)/g,rc={...eN,getAnimatableNone:t=>{let e=t.match(rh);return e?e.map(ru).join(" "):t}},rd={...tn,color:ek,backgroundColor:ek,outlineColor:ek,fill:ek,stroke:ek,borderColor:ek,borderTopColor:ek,borderRightColor:ek,borderBottomColor:ek,borderLeftColor:ek,filter:rc,WebkitFilter:rc},rp=t=>rd[t];function rm(t,e){let r=rp(t);return r!==rc&&(r=eN),r.getAnimatableNone?r.getAnimatableNone(e):void 0}let rf=t=>/^0[^.\s]+$/.test(t);function rg(t,e){return t[e]||t.default||t}let rv={skipAnimations:!1},ry=(t,e,r,i={})=>n=>{let o=rg(i,t)||{},s=o.delay||i.delay||0,{elapsed:a=0}=i;a-=t9(s);let l=function(t,e,r,i){let n,o;let s=ra(e,r);n=Array.isArray(r)?[...r]:[null,r];let a=void 0!==i.from?i.from:t.get(),l=[];for(let t=0;t<n.length;t++){var u;null===n[t]&&(n[t]=0===t?a:n[t-1]),("number"==typeof(u=n[t])?0===u:null!==u?"none"===u||"0"===u||rf(u):void 0)&&l.push(t),"string"==typeof n[t]&&"none"!==n[t]&&"0"!==n[t]&&(o=n[t])}if(s&&l.length&&o)for(let t=0;t<l.length;t++)n[l[t]]=rm(e,o);return n}(e,t,r,o),u=l[0],h=l[l.length-1],c=ra(t,u),d=ra(t,h);(0,t6.K)(c===d,`You are trying to animate ${t} from "${u}" to "${h}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${h} via the \`style\` property.`);let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{n(),o.onComplete&&o.onComplete()}};if(!function({when:t,delay:e,delayChildren:r,staggerChildren:i,staggerDirection:n,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(o)&&(p={...p,...rs(t,p)}),p.duration&&(p.duration=t9(p.duration)),p.repeatDelay&&(p.repeatDelay=t9(p.repeatDelay)),!c||!d||t7.current||!1===o.type||rv.skipAnimations)return function({keyframes:t,delay:e,onUpdate:r,onComplete:i}){let n=()=>(r&&r(t[t.length-1]),i&&i(),{time:0,speed:1,duration:0,play:tq.Z,pause:tq.Z,stop:tq.Z,then:t=>(t(),Promise.resolve()),cancel:tq.Z,complete:tq.Z});return e?e8({keyframes:[0,1],duration:0,delay:e,onComplete:n}):n()}(t7.current?{...p,delay:0}:p);if(!i.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){let r=function(t,e,{onUpdate:r,onComplete:i,...n}){let o,s;if(!(e7()&&rt.has(e)&&!n.repeatDelay&&"mirror"!==n.repeatType&&0!==n.damping&&"inertia"!==n.type))return!1;let a=!1,l=!1,u=()=>{s=new Promise(t=>{o=t})};u();let{keyframes:h,duration:c=300,ease:d,times:p}=n;if(re(e,n)){let t=e8({...n,repeat:0,delay:0}),e={done:!1,value:h[0]},r=[],i=0;for(;!e.done&&i<2e4;)e=t.sample(i),r.push(e.value),i+=10;p=void 0,h=r,c=i-10,d="linear"}let m=function(t,e,r,{delay:i=0,duration:n,repeat:o=0,repeatType:s="loop",ease:a,times:l}={}){let u={[e]:r};l&&(u.offset=l);let h=function t(e){if(e)return et(e)?ee(e):Array.isArray(e)?e.map(t):er[e]}(a);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:i,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"})}(t.owner.current,e,h,{...n,duration:c,ease:d,times:p}),f=()=>{l=!1,m.cancel()},g=()=>{l=!0,tE.Wi.update(f),o(),u()};return m.onfinish=()=>{l||(t.set(function(t,{repeat:e,repeatType:r="loop"}){let i=e&&"loop"!==r&&e%2==1?0:t.length-1;return t[i]}(h,n)),i&&i(),g())},{then:(t,e)=>s.then(t,e),attachTimeline:t=>(m.timeline=t,m.onfinish=null,tq.Z),get time(){return t8(m.currentTime||0)},set time(newTime){m.currentTime=t9(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return t8(c)},play:()=>{a||(m.play(),(0,tE.Pn)(f))},pause:()=>m.pause(),stop:()=>{if(a=!0,"idle"===m.playState)return;let{currentTime:e}=m;if(e){let r=e8({...n,autoplay:!1});t.setWithVelocity(r.sample(e-10).value,r.sample(e).value,10)}g()},complete:()=>{l||m.finish()},cancel:g}}(e,t,p);if(r)return r}return e8(p)};function rb(t){return!!(L(t)&&t.add)}let rx=t=>/^\-?\d*\.?\d+$/.test(t);function rw(t,e){-1===t.indexOf(e)&&t.push(e)}function rP(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}class rA{constructor(){this.subscriptions=[]}add(t){return rw(this.subscriptions,t),()=>rP(this.subscriptions,t)}notify(t,e,r){let i=this.subscriptions.length;if(i){if(1===i)this.subscriptions[0](t,e,r);else for(let n=0;n<i;n++){let i=this.subscriptions[n];i&&i(t,e,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let rT=t=>!isNaN(parseFloat(t)),rS={current:void 0};class rk{constructor(t,e={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(t,e=!0)=>{this.prev=this.current,this.current=t;let{delta:r,timestamp:i}=tE.frameData;this.lastUpdated!==i&&(this.timeDelta=r,this.lastUpdated=i,tE.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>tE.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:t})=>{t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=rT(this.current),this.owner=e.owner}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new rA);let r=this.events[t].add(e);return"change"===t?()=>{r(),tE.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,r){this.set(e),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return rS.current&&rS.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t,e;return this.canTrackVelocity?(t=parseFloat(this.current)-parseFloat(this.prev),(e=this.timeDelta)?1e3/e*t:0):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function rV(t,e){return new rk(t,e)}let rC=t=>e=>e.test(t),rE=[N,Q,J,K,te,tt,{test:t=>"auto"===t,parse:t=>t}],rM=t=>rE.find(rC(t)),rD=[...rE,ek,eN],rj=t=>rD.find(rC(t));function rR(t,e,{delay:r=0,transitionOverride:i,type:n}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:s,...a}=t.makeTargetAnimatable(e),l=t.getValue("willChange");i&&(o=i);let u=[],h=n&&t.animationState&&t.animationState.getState()[n];for(let e in a){let i=t.getValue(e),n=a[e];if(!i||void 0===n||h&&function({protectedKeys:t,needsAnimating:e},r){let i=t.hasOwnProperty(r)&&!0!==e[r];return e[r]=!1,i}(h,e))continue;let s={delay:r,elapsed:0,...rg(o||{},e)};if(window.HandoffAppearAnimations){let r=t.getProps()[p];if(r){let t=window.HandoffAppearAnimations(r,e,i,tE.Wi);null!==t&&(s.elapsed=t,s.isHandoff=!0)}}let c=!s.isHandoff&&!function(t,e){let r=t.get();if(!Array.isArray(e))return r!==e;for(let t=0;t<e.length;t++)if(e[t]!==r)return!0}(i,n);if("spring"===s.type&&(i.getVelocity()||s.velocity)&&(c=!1),i.animation&&(c=!1),c)continue;i.start(ry(e,i,n,t.shouldReduceMotion&&j.has(e)?{type:!1}:s));let d=i.animation;rb(l)&&(l.add(e),d.then(()=>l.remove(e))),u.push(d)}return s&&Promise.all(u).then(()=>{s&&function(t,e){let r=t4(t,e),{transitionEnd:i={},transition:n={},...o}=r?t.makeTargetAnimatable(r,!1):{};for(let e in o={...o,...i}){let r=tk(o[e]);t.hasValue(e)?t.getValue(e).set(r):t.addValue(e,rV(r))}}(t,s)}),u}function rL(t,e,r={}){let i=t4(t,e,r.custom),{transition:n=t.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(n=r.transitionOverride);let o=i?()=>Promise.all(rR(t,i,r)):()=>Promise.resolve(),s=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=n;return function(t,e,r=0,i=0,n=1,o){let s=[],a=(t.variantChildren.size-1)*i,l=1===n?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(rF).forEach((t,i)=>{t.notify("AnimationStart",e),s.push(rL(t,e,{...o,delay:r+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(s)}(t,e,o+i,s,a,r)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([o(),s(r.delay)]);{let[t,e]="beforeChildren"===a?[o,s]:[s,o];return t().then(()=>e())}}function rF(t,e){return t.sortNodePosition(e)}let rB=[...v].reverse(),rO=v.length;function rW(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class rI extends tZ{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:r})=>(function(t,e,r={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>rL(t,e,r)));else if("string"==typeof e)i=rL(t,e,r);else{let n="function"==typeof e?t4(t,e,r.custom):e;i=Promise.all(rR(t,n,r))}return i.then(()=>t.notify("AnimationComplete",e))})(t,e,r))),r={animate:rW(!0),whileInView:rW(),whileHover:rW(),whileTap:rW(),whileDrag:rW(),whileFocus:rW(),exit:rW()},i=!0,n=(e,r)=>{let i=t4(t,r);if(i){let{transition:t,transitionEnd:r,...n}=i;e={...e,...n,...r}}return e};function o(o,s){let a=t.getProps(),l=t.getVariantContext(!0)||{},u=[],h=new Set,c={},d=1/0;for(let e=0;e<rO;e++){var p;let m=rB[e],v=r[m],y=void 0!==a[m]?a[m]:l[m],b=f(y),x=m===s?v.isActive:null;!1===x&&(d=e);let w=y===l[m]&&y!==a[m]&&b;if(w&&i&&t.manuallyAnimateOnMount&&(w=!1),v.protectedKeys={...c},!v.isActive&&null===x||!y&&!v.prevProp||g(y)||"boolean"==typeof y)continue;let P=(p=v.prevProp,("string"==typeof y?y!==p:!!Array.isArray(y)&&!t3(y,p))||m===s&&v.isActive&&!w&&b||e>d&&b),A=!1,T=Array.isArray(y)?y:[y],S=T.reduce(n,{});!1===x&&(S={});let{prevResolvedValues:k={}}=v,V={...k,...S},C=t=>{P=!0,h.has(t)&&(A=!0,h.delete(t)),v.needsAnimating[t]=!0};for(let t in V){let e=S[t],r=k[t];if(!c.hasOwnProperty(t))(tT(e)&&tT(r)?t3(e,r):e===r)?void 0!==e&&h.has(t)?C(t):v.protectedKeys[t]=!0:void 0!==e?C(t):h.add(t)}v.prevProp=y,v.prevResolvedValues=S,v.isActive&&(c={...c,...S}),i&&t.blockInitialAnimation&&(P=!1),P&&(!w||A)&&u.push(...T.map(t=>({animation:t,options:{type:m,...o}})))}if(h.size){let e={};h.forEach(r=>{let i=t.getBaseTarget(r);void 0!==i&&(e[r]=i)}),u.push({animation:e})}let m=!!u.length;return i&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(m=!1),i=!1,m?e(u):Promise.resolve()}return{animateChanges:o,setActive:function(e,i,n){var s;if(r[e].isActive===i)return Promise.resolve();null===(s=t.variantChildren)||void 0===s||s.forEach(t=>{var r;return null===(r=t.animationState)||void 0===r?void 0:r.setActive(e,i)}),r[e].isActive=i;let a=o(n,e);for(let t in r)r[t].protectedKeys={};return a},setAnimateFunction:function(r){e=r(t)},getState:()=>r}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),g(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}let rz=0;class rU extends tZ{constructor(){super(...arguments),this.id=rz++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t,{custom:null!=r?r:this.node.getProps().custom});e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let rN=(t,e)=>Math.abs(t-e);class r${constructor(t,e,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=rG(this.lastMoveEventInfo,this.history),i=null!==this.startEvent,n=(t=r.offset,e={x:0,y:0},Math.sqrt(rN(t.x,e.x)**2+rN(t.y,e.y)**2)>=3);if(!i&&!n)return;let{point:o}=r,{timestamp:s}=tE.frameData;this.history.push({...o,timestamp:s});let{onStart:a,onMove:l}=this.handlers;i||(a&&a(this.lastMoveEvent,r),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,r)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=rZ(e,this.transformPagePoint),tE.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:r,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rG("pointercancel"===t.type?this.lastMoveEventInfo:rZ(e,this.transformPagePoint),this.history);this.startEvent&&r&&r(t,o),i&&i(t,o)},!tR(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=r,this.contextWindow=i||window;let o=rZ(tL(t),this.transformPagePoint),{point:s}=o,{timestamp:a}=tE.frameData;this.history=[{...s,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,rG(o,this.history)),this.removeListeners=tW(tB(this.contextWindow,"pointermove",this.handlePointerMove),tB(this.contextWindow,"pointerup",this.handlePointerUp),tB(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,tE.Pn)(this.updatePoint)}}function rZ(t,e){return e?{point:e(t.point)}:t}function rH(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rG({point:t},e){return{point:t,delta:rH(t,rY(e)),offset:rH(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let r=t.length-1,i=null,n=rY(t);for(;r>=0&&(i=t[r],!(n.timestamp-i.timestamp>t9(.1)));)r--;if(!i)return{x:0,y:0};let o=t8(n.timestamp-i.timestamp);if(0===o)return{x:0,y:0};let s={x:(n.x-i.x)/o,y:(n.y-i.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(e,0)}}function rY(t){return t[t.length-1]}function rX(t){return t.max-t.min}function rq(t,e=0,r=.01){return Math.abs(t-e)<=r}function r_(t,e,r,i=.5){t.origin=i,t.originPoint=eV(e.min,e.max,t.origin),t.scale=rX(r)/rX(e),(rq(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=eV(r.min,r.max,t.origin)-t.originPoint,(rq(t.translate)||isNaN(t.translate))&&(t.translate=0)}function rK(t,e,r,i){r_(t.x,e.x,r.x,i?i.originX:void 0),r_(t.y,e.y,r.y,i?i.originY:void 0)}function rJ(t,e,r){t.min=r.min+e.min,t.max=t.min+rX(e)}function rQ(t,e,r){t.min=e.min-r.min,t.max=t.min+rX(e)}function r0(t,e,r){rQ(t.x,e.x,r.x),rQ(t.y,e.y,r.y)}function r1(t,e,r){return{min:void 0!==e?t.min+e:void 0,max:void 0!==r?t.max+r-(t.max-t.min):void 0}}function r5(t,e){let r=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([r,i]=[i,r]),{min:r,max:i}}function r2(t,e,r){return{min:r3(t,e),max:r3(t,r)}}function r3(t,e){return"number"==typeof t?t:t[e]||0}let r4=()=>({translate:0,scale:1,origin:0,originPoint:0}),r6=()=>({x:r4(),y:r4()}),r9=()=>({min:0,max:0}),r8=()=>({x:r9(),y:r9()});function r7(t){return[t("x"),t("y")]}function it({top:t,left:e,right:r,bottom:i}){return{x:{min:e,max:r},y:{min:t,max:i}}}function ie(t){return void 0===t||1===t}function ir({scale:t,scaleX:e,scaleY:r}){return!ie(t)||!ie(e)||!ie(r)}function ii(t){return ir(t)||io(t)||t.z||t.rotate||t.rotateX||t.rotateY}function io(t){var e,r;return(e=t.x)&&"0%"!==e||(r=t.y)&&"0%"!==r}function is(t,e,r,i,n){return void 0!==n&&(t=i+n*(t-i)),i+r*(t-i)+e}function ia(t,e=0,r=1,i,n){t.min=is(t.min,e,r,i,n),t.max=is(t.max,e,r,i,n)}function il(t,{x:e,y:r}){ia(t.x,e.translate,e.scale,e.originPoint),ia(t.y,r.translate,r.scale,r.originPoint)}function iu(t){return Number.isInteger(t)?t:t>1.0000000000001||t<.999999999999?t:1}function ih(t,e){t.min=t.min+e,t.max=t.max+e}function ic(t,e,[r,i,n]){let o=void 0!==e[n]?e[n]:.5,s=eV(t.min,t.max,o);ia(t,e[r],e[i],s,e.scale)}let id=["x","scaleX","originX"],ip=["y","scaleY","originY"];function im(t,e){ic(t.x,e,id),ic(t.y,e,ip)}function ig(t,e){return it(function(t,e){if(!e)return t;let r=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:r.y,left:r.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}let iv=({current:t})=>t?t.ownerDocument.defaultView:null,iy=new WeakMap;class ib{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=r8(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new r$(t,{onSessionStart:t=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tL(t,"page").point)},onStart:(t,e)=>{let{drag:r,dragPropagation:i,onDragStart:n}=this.getProps();if(r&&!i&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=tN(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),r7(t=>{let e=this.getAxisMotionValue(t).get()||0;if(J.test(e)){let{projection:r}=this.visualElement;if(r&&r.layout){let i=r.layout.layoutBox[t];if(i){let t=rX(i);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),n&&tE.Wi.update(()=>n(t,e),!1,!0);let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:r,dragDirectionLock:i,onDirectionLock:n,onDrag:o}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:s}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let r=null;return Math.abs(t.y)>e?r="y":Math.abs(t.x)>e&&(r="x"),r}(s),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,s),this.updateAxis("y",e.point,s),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>r7(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:iv(this.visualElement)})}stop(t,e){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:n}=this.getProps();n&&tE.Wi.update(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,r){let{drag:i}=this.getProps();if(!r||!ix(t,i,this.currentDirection))return;let n=this.getAxisMotionValue(t),o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:r},i){return void 0!==e&&t<e?t=i?eV(e,t,i.min):Math.max(t,e):void 0!==r&&t>r&&(t=i?eV(r,t,i.max):Math.min(t,r)),t}(o,this.constraints[t],this.elastic[t])),n.set(o)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&m(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(t,{top:e,left:r,bottom:i,right:n}){return{x:r1(t.x,r,n),y:r1(t.y,e,i)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:r2(t,"left","right"),y:r2(t,"top","bottom")}}(r),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&r7(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let r={};return void 0!==e.min&&(r.min=e.min-t.min),void 0!==e.max&&(r.max=e.max-t.min),r}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:r}=this.getProps();if(!e||!m(e))return!1;let i=e.current;(0,t6.k)(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let o=function(t,e,r){let i=ig(t,r),{scroll:n}=e;return n&&(ih(i.x,n.offset.x),ih(i.y,n.offset.y)),i}(i,n.root,this.visualElement.getTransformPagePoint()),s={x:r5((t=n.layout.layoutBox).x,o.x),y:r5(t.y,o.y)};if(r){let t=r(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(s));this.hasMutatedConstraints=!!t,t&&(s=it(t))}return s}startAnimation(t){let{drag:e,dragMomentum:r,dragElastic:i,dragTransition:n,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(r7(s=>{if(!ix(s,e,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?t[s]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(t,e){let r=this.getAxisMotionValue(t);return r.start(ry(t,r,0,e))}stopAnimation(){r7(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){r7(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e="_drag"+t.toUpperCase(),r=this.visualElement.getProps();return r[e]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){r7(e=>{let{drag:r}=this.getProps();if(!ix(e,r,this.currentDirection))return;let{projection:i}=this.visualElement,n=this.getAxisMotionValue(e);if(i&&i.layout){let{min:r,max:o}=i.layout.layoutBox[e];n.set(t[e]-eV(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:r}=this.visualElement;if(!m(e)||!r||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};r7(t=>{let e=this.getAxisMotionValue(t);if(e){let r=e.get();i[t]=function(t,e){let r=.5,i=rX(t),n=rX(e);return n>i?r=eX(e.min,e.max-i,t.min):i>n&&(r=eX(t.min,t.max-n,e.min)),U(0,1,r)}({min:r,max:r},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),r7(e=>{if(!ix(e,t,null))return;let r=this.getAxisMotionValue(e),{min:n,max:o}=this.constraints[e];r.set(eV(n,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;iy.set(this.visualElement,this);let t=tB(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:r=!0}=this.getProps();e&&r&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();m(t)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,i=r.addEventListener("measure",e);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),e();let n=tj(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(r7(e=>{let r=this.getAxisMotionValue(e);r&&(this.originPoint[e]+=t[e].translate,r.set(r.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),i(),o&&o()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:o=.35,dragMomentum:s=!0}=t;return{...t,drag:e,dragDirectionLock:r,dragPropagation:i,dragConstraints:n,dragElastic:o,dragMomentum:s}}}function ix(t,e,r){return(!0===e||e===t)&&(null===r||r===t)}class iw extends tZ{constructor(t){super(t),this.removeGroupControls=tq.Z,this.removeListeners=tq.Z,this.controls=new ib(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tq.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let iP=t=>(e,r)=>{t&&tE.Wi.update(()=>t(e,r))};class iA extends tZ{constructor(){super(...arguments),this.removePointerDownListener=tq.Z}onPointerDown(t){this.session=new r$(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iv(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:iP(t),onStart:iP(e),onMove:r,onEnd:(t,e)=>{delete this.session,i&&tE.Wi.update(()=>i(t,e))}}}mount(){this.removePointerDownListener=tB(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let iT={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iS(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ik={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Q.test(t))return t;t=parseFloat(t)}let r=iS(t,e.target.x),i=iS(t,e.target.y);return`${r}% ${i}%`}};class iV extends s.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r,layoutId:i}=this.props,{projection:n}=t;Object.assign(M,iE),n&&(e.group&&e.group.add(n),r&&r.register&&i&&r.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),iT.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:r,drag:i,isPresent:n}=this.props,o=r.projection;return o&&(o.isPresent=n,i||t.layoutDependency!==e||void 0===e?o.willUpdate():this.safeToRemove(),t.isPresent===n||(n?o.promote():o.relegate()||tE.Wi.postRender(()=>{let t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function iC(t){let[e,r]=function(){let t=(0,s.useContext)(u.O);if(null===t)return[!0,null];let{isPresent:e,onExitComplete:r,register:i}=t,n=(0,s.useId)();return(0,s.useEffect)(()=>i(n),[]),!e&&r?[!1,()=>r&&r(n)]:[!0]}(),i=(0,s.useContext)(S.p);return s.createElement(iV,{...t,layoutGroup:i,switchLayoutGroup:(0,s.useContext)(k),isPresent:e,safeToRemove:r})}let iE={borderRadius:{...ik,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ik,borderTopRightRadius:ik,borderBottomLeftRadius:ik,borderBottomRightRadius:ik,boxShadow:{correct:(t,{treeScale:e,projectionDelta:r})=>{let i=eN.parse(t);if(i.length>5)return t;let n=eN.createTransformer(t),o="number"!=typeof i[0]?1:0,s=r.x.scale*e.x,a=r.y.scale*e.y;i[0+o]/=s,i[1+o]/=a;let l=eV(s,a,.5);return"number"==typeof i[2+o]&&(i[2+o]/=l),"number"==typeof i[3+o]&&(i[3+o]/=l),n(i)}}},iM=["TopLeft","TopRight","BottomLeft","BottomRight"],iD=iM.length,ij=t=>"string"==typeof t?parseFloat(t):t,iR=t=>"number"==typeof t||Q.test(t);function iL(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iF=iO(0,.5,ed),iB=iO(.5,.95,tq.Z);function iO(t,e,r){return i=>i<t?0:i>e?1:r(eX(t,e,i))}function iW(t,e){t.min=e.min,t.max=e.max}function iI(t,e){iW(t.x,e.x),iW(t.y,e.y)}function iz(t,e,r,i,n){return t-=e,t=i+1/r*(t-i),void 0!==n&&(t=i+1/n*(t-i)),t}function iU(t,e,[r,i,n],o,s){!function(t,e=0,r=1,i=.5,n,o=t,s=t){if(J.test(e)&&(e=parseFloat(e),e=eV(s.min,s.max,e/100)-s.min),"number"!=typeof e)return;let a=eV(o.min,o.max,i);t===o&&(a-=e),t.min=iz(t.min,e,r,a,n),t.max=iz(t.max,e,r,a,n)}(t,e[r],e[i],e[n],e.scale,o,s)}let iN=["x","scaleX","originX"],i$=["y","scaleY","originY"];function iZ(t,e,r,i){iU(t.x,e,iN,r?r.x:void 0,i?i.x:void 0),iU(t.y,e,i$,r?r.y:void 0,i?i.y:void 0)}function iH(t){return 0===t.translate&&1===t.scale}function iG(t){return iH(t.x)&&iH(t.y)}function iY(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function iX(t){return rX(t.x)/rX(t.y)}class iq{constructor(){this.members=[]}add(t){rw(this.members,t),t.scheduleRender()}remove(t){if(rP(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let r=this.members.findIndex(e=>t===e);if(0===r)return!1;for(let t=r;t>=0;t--){let r=this.members[t];if(!1!==r.isPresent){e=r;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,e&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:i}=t.options;!1===i&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:r}=t;e.onExitComplete&&e.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function i_(t,e,r){let i="",n=t.x.translate/e.x,o=t.y.translate/e.y;if((n||o)&&(i=`translate3d(${n}px, ${o}px, 0) `),(1!==e.x||1!==e.y)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),r){let{rotate:t,rotateX:e,rotateY:n}=r;t&&(i+=`rotate(${t}deg) `),e&&(i+=`rotateX(${e}deg) `),n&&(i+=`rotateY(${n}deg) `)}let s=t.x.scale*e.x,a=t.y.scale*e.y;return(1!==s||1!==a)&&(i+=`scale(${s}, ${a})`),i||"none"}let iK=(t,e)=>t.depth-e.depth;class iJ{constructor(){this.children=[],this.isDirty=!1}add(t){rw(this.children,t),this.isDirty=!0}remove(t){rP(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(iK),this.isDirty=!1,this.children.forEach(t)}}let iQ=["","X","Y","Z"],i0={visibility:"hidden"},i1=0,i5={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function i2({attachResizeListener:t,defaultParent:e,measureScroll:r,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(t={},r=null==e?void 0:e()){this.id=i1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,i5.totalNodes=i5.resolvedTargetDeltas=i5.recalculatedProjection=0,this.nodes.forEach(i6),this.nodes.forEach(ni),this.nodes.forEach(nn),this.nodes.forEach(i9),window.MotionDebug&&window.MotionDebug.record(i5)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new iJ)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new rA),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let r=this.eventHandlers.get(t);r&&r.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:i,layout:n,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(n||i)&&(this.isLayoutDirty=!0),t){let r;let i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(t,e){let r=performance.now(),i=({timestamp:e})=>{let n=e-r;n>=250&&((0,tE.Pn)(i),t(n-250))};return tE.Wi.read(i,!0),()=>(0,tE.Pn)(i)}(i,0),iT.hasAnimatedSinceResize&&(iT.hasAnimatedSinceResize=!1,this.nodes.forEach(nr))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:r,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||o.getDefaultTransition()||nh,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!iY(this.targetLayout,i)||r,u=!e&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...rg(n,"layout"),onPlay:s,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||nr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,tE.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(no),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:r}=this.options;if(void 0===e&&!r)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(i7);return}this.isUpdating||this.nodes.forEach(nt),this.isUpdating=!1,this.nodes.forEach(ne),this.nodes.forEach(i3),this.nodes.forEach(i4),this.clearAllSnapshots();let t=performance.now();tE.frameData.delta=U(0,1e3/60,t-tE.frameData.timestamp),tE.frameData.timestamp=t,tE.frameData.isProcessing=!0,tE.S6.update.process(tE.frameData),tE.S6.preRender.process(tE.frameData),tE.S6.render.process(tE.frameData),tE.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(i8),this.sharedNodes.forEach(ns)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tE.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tE.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=r8(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:i(this.instance),offset:r(this.instance)})}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!iG(this.projectionDelta),r=this.getTransformTemplate(),i=r?r(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&(e||ii(this.latestValues)||o)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let r=this.measurePageBox(),i=this.removeElementScroll(r);return t&&(i=this.removeTransform(i)),np((e=i).x),np(e.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return r8();let e=t.measureViewportBox(),{scroll:r}=this.root;return r&&(ih(e.x,r.offset.x),ih(e.y,r.offset.y)),e}removeElementScroll(t){let e=r8();iI(e,t);for(let r=0;r<this.path.length;r++){let i=this.path[r],{scroll:n,options:o}=i;if(i!==this.root&&n&&o.layoutScroll){if(n.isRoot){iI(e,t);let{scroll:r}=this.root;r&&(ih(e.x,-r.offset.x),ih(e.y,-r.offset.y))}ih(e.x,n.offset.x),ih(e.y,n.offset.y)}}return e}applyTransform(t,e=!1){let r=r8();iI(r,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&im(r,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),ii(i.latestValues)&&im(r,i.latestValues)}return ii(this.latestValues)&&im(r,this.latestValues),r}removeTransform(t){let e=r8();iI(e,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];if(!r.instance||!ii(r.latestValues))continue;ir(r.latestValues)&&r.updateSnapshot();let i=r8();iI(i,r.measurePageBox()),iZ(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}return ii(this.latestValues)&&iZ(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tE.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,r,i,n;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==o;if(!(t||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=tE.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r8(),this.relativeTargetOrigin=r8(),r0(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),iI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=r8(),this.targetWithTransforms=r8()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,i=this.relativeTarget,n=this.relativeParent.target,rJ(r.x,i.x,n.x),rJ(r.y,i.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iI(this.target,this.layout.layoutBox),il(this.target,this.targetDelta)):iI(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r8(),this.relativeTargetOrigin=r8(),r0(this.relativeTargetOrigin,this.target,t.target),iI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}i5.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||ir(this.parent.latestValues)||io(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),r=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===tE.frameData.timestamp&&(i=!1),i)return;let{layout:n,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||o))return;iI(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(t,e,r,i=!1){let n,o;let s=r.length;if(s){e.x=e.y=1;for(let a=0;a<s;a++){o=(n=r[a]).projectionDelta;let s=n.instance;(!s||!s.style||"contents"!==s.style.display)&&(i&&n.options.layoutScroll&&n.scroll&&n!==n.root&&im(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,il(t,o)),i&&ii(n.latestValues)&&im(t,n.latestValues))}e.x=iu(e.x),e.y=iu(e.y)}}(this.layoutCorrected,this.treeScale,this.path,r),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox);let{target:l}=e;if(!l){this.projectionTransform&&(this.projectionDelta=r6(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=r6(),this.projectionDeltaWithTransform=r6());let u=this.projectionTransform;rK(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=i_(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==s||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),i5.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){let r;let i=this.snapshot,n=i?i.latestValues:{},o={...this.latestValues},s=r6();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=r8(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nu));this.animationProgress=0,this.mixTargetDelta=e=>{let i=e/1e3;if(na(s.x,t.x,i),na(s.y,t.y,i),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m;r0(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,nl(p.x,m.x,a.x,i),nl(p.y,m.y,a.y,i),r&&(u=this.relativeTarget,d=r,u.x.min===d.x.min&&u.x.max===d.x.max&&u.y.min===d.y.min&&u.y.max===d.y.max)&&(this.isProjectionDirty=!1),r||(r=r8()),iI(r,this.relativeTarget)}l&&(this.animationValues=o,function(t,e,r,i,n,o){n?(t.opacity=eV(0,void 0!==r.opacity?r.opacity:1,iF(i)),t.opacityExit=eV(void 0!==e.opacity?e.opacity:1,0,iB(i))):o&&(t.opacity=eV(void 0!==e.opacity?e.opacity:1,void 0!==r.opacity?r.opacity:1,i));for(let n=0;n<iD;n++){let o=`border${iM[n]}Radius`,s=iL(e,o),a=iL(r,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||iR(s)===iR(a)?(t[o]=Math.max(eV(ij(s),ij(a),i),0),(J.test(a)||J.test(s))&&(t[o]+="%")):t[o]=a)}(e.rotate||r.rotate)&&(t.rotate=eV(e.rotate||0,r.rotate||0,i))}(o,n,this.latestValues,i,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,tE.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tE.Wi.update(()=>{iT.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,r){let i=L(0)?0:rV(0);return i.start(ry("",i,1e3,r)),i.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:r,layout:i,latestValues:n}=t;if(e&&r&&i){if(this!==t&&this.layout&&i&&nm(this.options.animationType,this.layout.layoutBox,i.layoutBox)){r=this.target||r8();let e=rX(this.layout.layoutBox.x);r.x.min=t.target.x.min,r.x.max=r.x.min+e;let i=rX(this.layout.layoutBox.y);r.y.min=t.target.y.min,r.y.max=r.y.min+i}iI(e,r),im(e,n),rK(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iq),this.sharedNodes.get(t).add(e);let r=e.options.initialPromotionConfig;e.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:r}={}){let i=this.getStack();i&&i.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:r}=t;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(e=!0),!e)return;let i={};for(let e=0;e<iQ.length;e++){let n="rotate"+iQ[e];r[n]&&(i[n]=r[n],t.setStaticValue(n,0))}for(let e in t.render(),i)t.setStaticValue(e,i[e]);t.scheduleRender()}getProjectionStyles(t){var e,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return i0;let i={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=tV(null==t?void 0:t.pointerEvents)||"",i.transform=n?n(this.latestValues,""):"none",i;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tV(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!ii(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let s=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=i_(this.projectionDeltaWithTransform,this.treeScale,s),n&&(i.transform=n(s,i.transform));let{x:a,y:l}=this.projectionDelta;for(let t in i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(r=null!==(e=s.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:i.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,M){if(void 0===s[t])continue;let{correct:e,applyTo:r}=M[t],n="none"===i.transform?s[t]:e(s[t],o);if(r){let t=r.length;for(let e=0;e<t;e++)i[r[e]]=n}else i[t]=n}return this.options.layoutId&&(i.pointerEvents=o===this?tV(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(i7),this.root.sharedNodes.clear()}}}function i3(t){t.updateLayout()}function i4(t){var e;let r=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&r&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:i}=t.layout,{animationType:n}=t.options,o=r.source!==t.layout.source;"size"===n?r7(t=>{let i=o?r.measuredBox[t]:r.layoutBox[t],n=rX(i);i.min=e[t].min,i.max=i.min+n}):nm(n,r.layoutBox,e)&&r7(i=>{let n=o?r.measuredBox[i]:r.layoutBox[i],s=rX(e[i]);n.max=n.min+s,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+s)});let s=r6();rK(s,e,r.layoutBox);let a=r6();o?rK(a,t.applyTransform(i,!0),r.measuredBox):rK(a,e,r.layoutBox);let l=!iG(s),u=!1;if(!t.resumeFrom){let i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:n,layout:o}=i;if(n&&o){let s=r8();r0(s,r.layoutBox,n.layoutBox);let a=r8();r0(a,e,o.layoutBox),iY(s,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=s,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:r,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function i6(t){i5.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function i9(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function i8(t){t.clearSnapshot()}function i7(t){t.clearMeasurements()}function nt(t){t.isLayoutDirty=!1}function ne(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ni(t){t.resolveTargetDelta()}function nn(t){t.calcProjection()}function no(t){t.resetRotation()}function ns(t){t.removeLeadSnapshot()}function na(t,e,r){t.translate=eV(e.translate,0,r),t.scale=eV(e.scale,1,r),t.origin=e.origin,t.originPoint=e.originPoint}function nl(t,e,r,i){t.min=eV(e.min,r.min,i),t.max=eV(e.max,r.max,i)}function nu(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nh={duration:.45,ease:[.4,0,.1,1]},nc=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),nd=nc("applewebkit/")&&!nc("chrome/")?Math.round:tq.Z;function np(t){t.min=nd(t.min),t.max=nd(t.max)}function nm(t,e,r){return"position"===t||"preserve-aspect"===t&&!rq(iX(e),iX(r),.2)}let nf=i2({attachResizeListener:(t,e)=>tj(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ng={current:void 0},nv=i2({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ng.current){let t=new nf({});t.mount(window),t.setOptions({layoutScroll:!0}),ng.current=t}return ng.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position}),ny=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function nb(t,e,r=1){(0,t6.k)(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,n]=function(t){let e=ny.exec(t);if(!e)return[,];let[,r,i]=e;return[r,i]}(t);if(!i)return;let o=window.getComputedStyle(e).getPropertyValue(i);if(o){let t=o.trim();return rx(t)?parseFloat(t):t}return I(n)?nb(n,e,r+1):n}let nx=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),nw=t=>nx.has(t),nP=t=>Object.keys(t).some(nw),nA=t=>t===N||t===Q,nT=(t,e)=>parseFloat(t.split(", ")[e]),nS=(t,e)=>(r,{transform:i})=>{if("none"===i||!i)return 0;let n=i.match(/^matrix3d\((.+)\)$/);if(n)return nT(n[1],e);{let e=i.match(/^matrix\((.+)\)$/);return e?nT(e[1],t):0}},nk=new Set(["x","y","z"]),nV=D.filter(t=>!nk.has(t)),nC={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:nS(4,13),y:nS(5,14)};nC.translateX=nC.x,nC.translateY=nC.y;let nE=(t,e,r)=>{let i=e.measureViewportBox(),n=getComputedStyle(e.current),{display:o}=n,s={};"none"===o&&e.setStaticValue("display",t.display||"block"),r.forEach(t=>{s[t]=nC[t](i,n)}),e.render();let a=e.measureViewportBox();return r.forEach(r=>{let i=e.getValue(r);i&&i.jump(s[r]),t[r]=nC[r](a,n)}),t},nM=(t,e,r={},i={})=>{e={...e},i={...i};let n=Object.keys(e).filter(nw),o=[],s=!1,a=[];if(n.forEach(n=>{let l;let u=t.getValue(n);if(!t.hasValue(n))return;let h=r[n],c=rM(h),d=e[n];if(tT(d)){let t=d.length,e=null===d[0]?1:0;c=rM(h=d[e]);for(let r=e;r<t&&null!==d[r];r++)l?(0,t6.k)(rM(d[r])===l,"All keyframes must be of the same type"):(l=rM(d[r]),(0,t6.k)(l===c||nA(c)&&nA(l),"Keyframes must be of the same dimension as the current value"))}else l=rM(d);if(c!==l){if(nA(c)&&nA(l)){let t=u.get();"string"==typeof t&&u.set(parseFloat(t)),"string"==typeof d?e[n]=parseFloat(d):Array.isArray(d)&&l===Q&&(e[n]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===h||0===d)?0===h?u.set(l.transform(h)):e[n]=c.transform(d):(s||(o=function(t){let e=[];return nV.forEach(r=>{let i=t.getValue(r);void 0!==i&&(e.push([r,i.get()]),i.set(r.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),s=!0),a.push(n),i[n]=void 0!==i[n]?i[n]:e[n],u.jump(d))}}),!a.length)return{target:e,transitionEnd:i};{let r=a.indexOf("height")>=0?window.pageYOffset:null,n=nE(e,t,a);return o.length&&o.forEach(([e,r])=>{t.getValue(e).set(r)}),t.render(),T.j&&null!==r&&window.scrollTo({top:r}),{target:n,transitionEnd:i}}},nD=(t,e,r,i)=>{var n,o;let s=function(t,{...e},r){let i=t.current;if(!(i instanceof Element))return{target:e,transitionEnd:r};for(let n in r&&(r={...r}),t.values.forEach(t=>{let e=t.get();if(!I(e))return;let r=nb(e,i);r&&t.set(r)}),e){let t=e[n];if(!I(t))continue;let o=nb(t,i);o&&(e[n]=o,r||(r={}),void 0===r[n]&&(r[n]=t))}return{target:e,transitionEnd:r}}(t,e,i);return e=s.target,i=s.transitionEnd,n=e,o=i,nP(n)?nM(t,n,r,o):{target:n,transitionEnd:o}},nj={current:null},nR={current:!1},nL=new WeakMap,nF=Object.keys(A),nB=nF.length,nO=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],nW=y.length;class nI{constructor({parent:t,props:e,presenceContext:r,reducedMotionConfig:i,visualState:n},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>tE.Wi.render(this.render,!1,!0);let{latestValues:s,renderState:a}=n;this.latestValues=s,this.baseTarget={...s},this.initialValues=e.initial?{...s}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=b(e),this.isVariantNode=x(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{});for(let t in u){let e=u[t];void 0!==s[t]&&L(e)&&(e.set(s[t],!1),rb(l)&&l.add(t))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,nL.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nR.current||function(){if(nR.current=!0,T.j){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>nj.current=t.matches;t.addListener(e),e()}else nj.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nj.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in nL.delete(this.current),this.projection&&this.projection.unmount(),(0,tE.Pn)(this.notifyUpdate),(0,tE.Pn)(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){let r=j.has(t),i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tE.Wi.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),n()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},r,i,n){let o,s;for(let t=0;t<nB;t++){let r=nF[t],{isEnabled:i,Feature:n,ProjectionNode:a,MeasureLayout:l}=A[r];a&&(o=a),i(e)&&(!this.features[r]&&n&&(this.features[r]=new n(this)),l&&(s=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);let{layoutId:t,layout:r,drag:i,dragConstraints:s,layoutScroll:a,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:r,alwaysMeasureLayout:!!i||s&&m(s),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,layoutScroll:a,layoutRoot:l})}return s}updateFeatures(){for(let t in this.features){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):r8()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nO.length;e++){let r=nO[e];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(t,e,r){let{willChange:i}=e;for(let n in e){let o=e[n],s=r[n];if(L(o))t.addValue(n,o),rb(i)&&i.add(n);else if(L(s))t.addValue(n,rV(o,{owner:t})),rb(i)&&i.remove(n);else if(s!==o){if(t.hasValue(n)){let e=t.getValue(n);e.hasAnimated||e.set(o)}else{let e=t.getStaticValue(n);t.addValue(n,rV(void 0!==e?e:o,{owner:t}))}}}for(let i in r)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}let e={};for(let t=0;t<nW;t++){let r=y[t],i=this.props[r];(f(i)||!1===i)&&(e[r]=i)}return e}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return void 0===r&&void 0!==e&&(r=rV(e,{owner:this}),this.addValue(t,r)),r}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let{initial:r}=this.props,i="string"==typeof r||"object"==typeof r?null===(e=tP(this.props,r))||void 0===e?void 0:e[t]:void 0;if(r&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||L(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new rA),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nz extends nI{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:r}){delete e[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...r},{transformValues:i},n){let o=function(t,e,r){let i={};for(let n in t){let t=function(t,e){if(e)return(e[t]||e.default||e).from}(n,e);if(void 0!==t)i[n]=t;else{let t=r.getValue(n);t&&(i[n]=t.get())}}return i}(r,t||{},this);if(i&&(e&&(e=i(e)),r&&(r=i(r)),o&&(o=i(o))),n){!function(t,e,r){var i,n;let o=Object.keys(e).filter(e=>!t.hasValue(e)),s=o.length;if(s)for(let a=0;a<s;a++){let s=o[a],l=e[s],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(n=null!==(i=r[s])&&void 0!==i?i:t.readValue(s))&&void 0!==n?n:e[s]),null!=u&&("string"==typeof u&&(rx(u)||rf(u))?u=parseFloat(u):!rj(u)&&eN.test(l)&&(u=rm(s,l)),t.addValue(s,rV(u,{owner:t})),void 0===r[s]&&(r[s]=u),null!==u&&t.setBaseTarget(s,u))}}(this,r,o);let t=nD(this,r,o,e);e=t.transitionEnd,r=t.target}return{transition:t,transitionEnd:e,...r}}}class nU extends nz{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(j.has(e)){let t=rp(e);return t&&t.default||0}{let r=window.getComputedStyle(t),i=(W(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ig(t,e)}build(t,e,r,i){to(t,e,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,e){return tx(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;L(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(t,e,r,i){tv(t,e,r,i)}}class nN extends nz{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(j.has(e)){let t=rp(e);return t&&t.default||0}return e=ty.has(e)?e:d(e),t.getAttribute(e)}measureInstanceViewportBox(){return r8()}scrapeMotionValuesFromProps(t,e){return tw(t,e)}build(t,e,r,i){tm(t,e,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,r,i){tb(t,e,r,i)}mount(t){this.isSVGTag=tg(t.tagName),super.mount(t)}}let n$=(t,e)=>E(t)?new nN(e,{enableHardwareAcceleration:!1}):new nU(e,{enableHardwareAcceleration:!0}),nZ={animation:{Feature:rI},exit:{Feature:rU},inView:{Feature:t2},tap:{Feature:tK},focus:{Feature:tY},hover:{Feature:tG},pan:{Feature:iA},drag:{Feature:iw,ProjectionNode:nv,MeasureLayout:iC},layout:{ProjectionNode:nv,MeasureLayout:iC}},nH=function(t){function e(e,r={}){return function({preloadedFeatures:t,createVisualElement:e,useRender:r,useVisualState:i,Component:n}){t&&function(t){for(let e in t)A[e]={...A[e],...t[e]}}(t);let o=(0,s.forwardRef)(function(o,d){var g;let v;let y={...(0,s.useContext)(a),...o,layoutId:function({layoutId:t}){let e=(0,s.useContext)(S.p).id;return e&&void 0!==t?e+"-"+t:t}(o)},{isStatic:x}=y,P=function(t){let{initial:e,animate:r}=function(t,e){if(b(t)){let{initial:e,animate:r}=t;return{initial:!1===e||f(e)?e:void 0,animate:f(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,s.useContext)(l));return(0,s.useMemo)(()=>({initial:e,animate:r}),[w(e),w(r)])}(o),A=i(o,x);if(!x&&T.j){P.visualElement=function(t,e,r,i){let{visualElement:n}=(0,s.useContext)(l),o=(0,s.useContext)(c),d=(0,s.useContext)(u.O),m=(0,s.useContext)(a).reducedMotion,f=(0,s.useRef)();i=i||o.renderer,!f.current&&i&&(f.current=i(t,{visualState:e,parent:n,props:r,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:m}));let g=f.current;(0,s.useInsertionEffect)(()=>{g&&g.update(r,d)});let v=(0,s.useRef)(!!(r[p]&&!window.HandoffComplete));return(0,h.L)(()=>{g&&(g.render(),v.current&&g.animationState&&g.animationState.animateChanges())}),(0,s.useEffect)(()=>{g&&(g.updateFeatures(),!v.current&&g.animationState&&g.animationState.animateChanges(),v.current&&(v.current=!1,window.HandoffComplete=!0))}),g}(n,A,y,e);let r=(0,s.useContext)(k),i=(0,s.useContext)(c).strict;P.visualElement&&(v=P.visualElement.loadFeatures(y,i,t,r))}return s.createElement(l.Provider,{value:P},v&&P.visualElement?s.createElement(v,{visualElement:P.visualElement,...y}):null,r(n,o,(g=P.visualElement,(0,s.useCallback)(t=>{t&&A.mount&&A.mount(t),g&&(t?g.mount(t):g.unmount()),d&&("function"==typeof d?d(t):m(d)&&(d.current=t))},[g])),A,x,P.visualElement))});return o[V]=n,o}(t(e,r))}if("undefined"==typeof Proxy)return e;let r=new Map;return new Proxy(e,{get:(t,i)=>(r.has(i)||r.set(i,e(i)),r.get(i))})}((t,e)=>(function(t,{forwardMotionProps:e=!1},r,i){return{...E(t)?tM:tD,preloadedFeatures:r,useRender:function(t=!1){return(e,r,i,{latestValues:n},o)=>{let a=(E(e)?function(t,e,r,i){let n=(0,s.useMemo)(()=>{let r=tf();return tm(r,e,{enableHardwareAcceleration:!1},tg(i),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){let e={};ta(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e,r){let i={},n=function(t,e,r){let i=t.style||{},n={};return ta(n,i,t),Object.assign(n,function({transformTemplate:t},e,r){return(0,s.useMemo)(()=>{let i=ts();return to(i,e,{enableHardwareAcceleration:!r},t),Object.assign({},i.vars,i.style)},[e])}(t,e,r)),t.transformValues?t.transformValues(n):n}(t,e,r);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(r,n,o,e),l={...function(t,e,r){let i={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(th(n)||!0===r&&tu(n)||!e&&!tu(n)||t.draggable&&n.startsWith("onDrag"))&&(i[n]=t[n]);return i}(r,"string"==typeof e,t),...a,ref:i},{children:u}=r,h=(0,s.useMemo)(()=>L(u)?u.get():u,[u]);return(0,s.createElement)(e,{...l,children:h})}}(e),createVisualElement:i,Component:t}})(t,e,nZ,n$))},3223:function(t,e,r){r.d(e,{K:function(){return n},k:function(){return o}});var i=r(4439);let n=i.Z,o=i.Z},4563:function(t,e,r){r.d(e,{j:function(){return i}});let i="undefined"!=typeof document},4439:function(t,e,r){r.d(e,{Z:function(){return i}});let i=t=>t},3576:function(t,e,r){r.d(e,{h:function(){return n}});var i=r(2265);function n(t){let e=(0,i.useRef)(null);return null===e.current&&(e.current=t()),e.current}},1534:function(t,e,r){r.d(e,{L:function(){return n}});var i=r(2265);let n=r(4563).j?i.useLayoutEffect:i.useEffect},8301:function(t,e,r){r.d(e,{Z:function(){return s}});var i=r(2265),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();var s=(t,e)=>{let r=(0,i.forwardRef)((r,s)=>{let{color:a="currentColor",size:l=24,strokeWidth:u=2,absoluteStrokeWidth:h,children:c,...d}=r;return(0,i.createElement)("svg",{ref:s,...n,width:l,height:l,stroke:a,strokeWidth:h?24*Number(u)/Number(l):u,className:"lucide lucide-".concat(o(t)),...d},[...e.map(t=>{let[e,r]=t;return(0,i.createElement)(e,r)}),...(Array.isArray(c)?c:[c])||[]])});return r.displayName="".concat(t),r}},1585:function(t,e,r){r.d(e,{Z:function(){return i}});let i=(0,r(8301).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["path",{d:"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z",key:"nb9nel"}]])},3335:function(t,e,r){r.d(e,{m6:function(){return q}});let i=t=>{let e=a(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=t;return{getClassGroupId:t=>{let r=t.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,e)||s(t)},getConflictingClassGroupIds:(t,e)=>{let n=r[t]||[];return e&&i[t]?[...n,...i[t]]:n}}},n=(t,e)=>{if(0===t.length)return e.classGroupId;let r=t[0],i=e.nextPart.get(r),o=i?n(t.slice(1),i):void 0;if(o)return o;if(0===e.validators.length)return;let s=t.join("-");return e.validators.find(({validator:t})=>t(s))?.classGroupId},o=/^\[(.+)\]$/,s=t=>{if(o.test(t)){let e=o.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},a=t=>{let{theme:e,prefix:r}=t,i={nextPart:new Map,validators:[]};return c(Object.entries(t.classGroups),r).forEach(([t,r])=>{l(r,i,t,e)}),i},l=(t,e,r,i)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=r;return}if("function"==typeof t){if(h(t)){l(t(i),e,r,i);return}e.validators.push({validator:t,classGroupId:r});return}Object.entries(t).forEach(([t,n])=>{l(n,u(e,t),r,i)})})},u=(t,e)=>{let r=t;return e.split("-").forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},h=t=>t.isThemeGetter,c=(t,e)=>e?t.map(([t,r])=>[t,r.map(t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map(([t,r])=>[e+t,r])):t)]):t,d=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,r=new Map,i=new Map,n=(n,o)=>{r.set(n,o),++e>t&&(e=0,i=r,r=new Map)};return{get(t){let e=r.get(t);return void 0!==e?e:void 0!==(e=i.get(t))?(n(t,e),e):void 0},set(t,e){r.has(t)?r.set(t,e):n(t,e)}}},p=t=>{let{separator:e,experimentalParseClassName:r}=t,i=1===e.length,n=e[0],o=e.length,s=t=>{let r;let s=[],a=0,l=0;for(let u=0;u<t.length;u++){let h=t[u];if(0===a){if(h===n&&(i||t.slice(u,u+o)===e)){s.push(t.slice(l,u)),l=u+o;continue}if("/"===h){r=u;continue}}"["===h?a++:"]"===h&&a--}let u=0===s.length?t:t.substring(l),h=u.startsWith("!"),c=h?u.substring(1):u;return{modifiers:s,hasImportantModifier:h,baseClassName:c,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?t=>r({className:t,parseClassName:s}):s},m=t=>{if(t.length<=1)return t;let e=[],r=[];return t.forEach(t=>{"["===t[0]?(e.push(...r.sort(),t),r=[]):r.push(t)}),e.push(...r.sort()),e},f=t=>({cache:d(t.cacheSize),parseClassName:p(t),...i(t)}),g=/\s+/,v=(t,e)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n}=e,o=[],s=t.trim().split(g),a="";for(let t=s.length-1;t>=0;t-=1){let e=s[t],{modifiers:l,hasImportantModifier:u,baseClassName:h,maybePostfixModifierPosition:c}=r(e),d=!!c,p=i(d?h.substring(0,c):h);if(!p){if(!d||!(p=i(h))){a=e+(a.length>0?" "+a:a);continue}d=!1}let f=m(l).join(":"),g=u?f+"!":f,v=g+p;if(o.includes(v))continue;o.push(v);let y=n(p,d);for(let t=0;t<y.length;++t){let e=y[t];o.push(g+e)}a=e+(a.length>0?" "+a:a)}return a};function y(){let t,e,r=0,i="";for(;r<arguments.length;)(t=arguments[r++])&&(e=b(t))&&(i&&(i+=" "),i+=e);return i}let b=t=>{let e;if("string"==typeof t)return t;let r="";for(let i=0;i<t.length;i++)t[i]&&(e=b(t[i]))&&(r&&(r+=" "),r+=e);return r},x=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,P=/^\d+\/\d+$/,A=new Set(["px","full","screen"]),T=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,S=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,k=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,V=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,C=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,E=t=>D(t)||A.has(t)||P.test(t),M=t=>Z(t,"length",H),D=t=>!!t&&!Number.isNaN(Number(t)),j=t=>Z(t,"number",D),R=t=>!!t&&Number.isInteger(Number(t)),L=t=>t.endsWith("%")&&D(t.slice(0,-1)),F=t=>w.test(t),B=t=>T.test(t),O=new Set(["length","size","percentage"]),W=t=>Z(t,O,G),I=t=>Z(t,"position",G),z=new Set(["image","url"]),U=t=>Z(t,z,X),N=t=>Z(t,"",Y),$=()=>!0,Z=(t,e,r)=>{let i=w.exec(t);return!!i&&(i[1]?"string"==typeof e?i[1]===e:e.has(i[1]):r(i[2]))},H=t=>S.test(t)&&!k.test(t),G=()=>!1,Y=t=>V.test(t),X=t=>C.test(t),q=function(t,...e){let r,i,n;let o=function(a){return i=(r=f(e.reduce((t,e)=>e(t),t()))).cache.get,n=r.cache.set,o=s,s(a)};function s(t){let e=i(t);if(e)return e;let o=v(t,r);return n(t,o),o}return function(){return o(y.apply(null,arguments))}}(()=>{let t=x("colors"),e=x("spacing"),r=x("blur"),i=x("brightness"),n=x("borderColor"),o=x("borderRadius"),s=x("borderSpacing"),a=x("borderWidth"),l=x("contrast"),u=x("grayscale"),h=x("hueRotate"),c=x("invert"),d=x("gap"),p=x("gradientColorStops"),m=x("gradientColorStopPositions"),f=x("inset"),g=x("margin"),v=x("opacity"),y=x("padding"),b=x("saturate"),w=x("scale"),P=x("sepia"),A=x("skew"),T=x("space"),S=x("translate"),k=()=>["auto","contain","none"],V=()=>["auto","hidden","clip","visible","scroll"],C=()=>["auto",F,e],O=()=>[F,e],z=()=>["",E,M],Z=()=>["auto",D,F],H=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],G=()=>["solid","dashed","dotted","double","none"],Y=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],X=()=>["start","end","center","between","around","evenly","stretch"],q=()=>["","0",F],_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],K=()=>[D,F];return{cacheSize:500,separator:":",theme:{colors:[$],spacing:[E,M],blur:["none","",B,F],brightness:K(),borderColor:[t],borderRadius:["none","","full",B,F],borderSpacing:O(),borderWidth:z(),contrast:K(),grayscale:q(),hueRotate:K(),invert:q(),gap:O(),gradientColorStops:[t],gradientColorStopPositions:[L,M],inset:C(),margin:C(),opacity:K(),padding:O(),saturate:K(),scale:K(),sepia:q(),skew:K(),space:O(),translate:O()},classGroups:{aspect:[{aspect:["auto","square","video",F]}],container:["container"],columns:[{columns:[B]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...H(),F]}],overflow:[{overflow:V()}],"overflow-x":[{"overflow-x":V()}],"overflow-y":[{"overflow-y":V()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",R,F]}],basis:[{basis:C()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",F]}],grow:[{grow:q()}],shrink:[{shrink:q()}],order:[{order:["first","last","none",R,F]}],"grid-cols":[{"grid-cols":[$]}],"col-start-end":[{col:["auto",{span:["full",R,F]},F]}],"col-start":[{"col-start":Z()}],"col-end":[{"col-end":Z()}],"grid-rows":[{"grid-rows":[$]}],"row-start-end":[{row:["auto",{span:[R,F]},F]}],"row-start":[{"row-start":Z()}],"row-end":[{"row-end":Z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",F]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",F]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...X()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...X(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...X(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[T]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[T]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",F,e]}],"min-w":[{"min-w":[F,e,"min","max","fit"]}],"max-w":[{"max-w":[F,e,"none","full","min","max","fit","prose",{screen:[B]},B]}],h:[{h:[F,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[F,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[F,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[F,e,"auto","min","max","fit"]}],"font-size":[{text:["base",B,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",j]}],"font-family":[{font:[$]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",F]}],"line-clamp":[{"line-clamp":["none",D,j]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",E,F]}],"list-image":[{"list-image":["none",F]}],"list-style-type":[{list:["none","disc","decimal",F]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...G(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",E,M]}],"underline-offset":[{"underline-offset":["auto",E,F]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:O()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...H(),I]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",W]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},U]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...G(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:G()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...G()]}],"outline-offset":[{"outline-offset":[E,F]}],"outline-w":[{outline:[E,M]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[E,M]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",B,N]}],"shadow-color":[{shadow:[$]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...Y(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Y()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[i]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",B,F]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[c]}],saturate:[{saturate:[b]}],sepia:[{sepia:[P]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[i]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[P]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",F]}],duration:[{duration:K()}],ease:[{ease:["linear","in","out","in-out",F]}],delay:[{delay:K()}],animate:[{animate:["none","spin","ping","pulse","bounce",F]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[R,F]}],"translate-x":[{"translate-x":[S]}],"translate-y":[{"translate-y":[S]}],"skew-x":[{"skew-x":[A]}],"skew-y":[{"skew-y":[A]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",F]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":O()}],"scroll-mx":[{"scroll-mx":O()}],"scroll-my":[{"scroll-my":O()}],"scroll-ms":[{"scroll-ms":O()}],"scroll-me":[{"scroll-me":O()}],"scroll-mt":[{"scroll-mt":O()}],"scroll-mr":[{"scroll-mr":O()}],"scroll-mb":[{"scroll-mb":O()}],"scroll-ml":[{"scroll-ml":O()}],"scroll-p":[{"scroll-p":O()}],"scroll-px":[{"scroll-px":O()}],"scroll-py":[{"scroll-py":O()}],"scroll-ps":[{"scroll-ps":O()}],"scroll-pe":[{"scroll-pe":O()}],"scroll-pt":[{"scroll-pt":O()}],"scroll-pr":[{"scroll-pr":O()}],"scroll-pb":[{"scroll-pb":O()}],"scroll-pl":[{"scroll-pl":O()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[E,M,j]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}}]);