# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# 构建输出
.next/
out/
build/
dist/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 缓存文件
.cache/
.parcel-cache/
.nyc_output/
cache/

# 运行时文件
*.tsbuildinfo
*.log

# 操作系统文件
.DS_Store
Thumbs.db

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# 测试文件
coverage/
.nyc_output/

# 临时文件
tmp/
temp/
*.tmp

# 自定义忽略
domain-search/
test-*.mjs
test-*.js
test-*.sh
*.txt
*.md
!README.md
!DEPLOYMENT.md
!CLOUDFLARE_DEPLOYMENT.md
start-dev.sh
implementation-summary.md
enhanced-rdap-implementation.md