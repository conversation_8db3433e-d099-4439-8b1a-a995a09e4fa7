var d={},j=(b,f,z)=>(d.__chunk_5285=(p,l,a)=>{a.d(l,{Z:()=>e});let e=(0,a(8622).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},d.__chunk_9839=(p,l,a)=>{a.d(l,{JS:()=>N,uo:()=>g});var e=a(926),r=a(9220),u=a(1464);function m({className:s="",children:t,spotlightColor:n="rgba(20, 128, 255, 0.15)",size:o=300}){let i=(0,r.useRef)(null),[c,x]=(0,r.useState)({x:0,y:0}),[h,_]=(0,r.useState)(!1);return(0,e.jsxs)("div",{ref:i,className:`relative overflow-hidden ${s}`,style:{isolation:"isolate"},children:[(0,e.jsx)(u.E.div,{className:"pointer-events-none absolute inset-0 z-0",animate:{background:h?`radial-gradient(${o}px circle at ${c.x}px ${c.y}px, ${n}, transparent 70%)`:"transparent"},transition:{background:{duration:.2,ease:"easeOut"}}}),(0,e.jsx)("div",{className:"relative z-10",children:t})]})}function v({className:s="",children:t,size:n=200}){let o=(0,r.useRef)(null),[i,c]=(0,r.useState)({x:0,y:0}),[x,h]=(0,r.useState)(!1);return(0,e.jsxs)("div",{ref:o,className:`relative overflow-hidden ${s}`,style:{isolation:"isolate"},children:[(0,e.jsx)(u.E.div,{className:"pointer-events-none absolute inset-0 z-0",animate:{background:x?`radial-gradient(${n}px circle at ${i.x}px ${i.y}px, rgba(33, 150, 243, 0.08), transparent 60%)`:"transparent"},transition:{background:{duration:.3,ease:"easeOut"}}}),(0,e.jsx)("div",{className:"relative z-10",children:t})]})}function g({className:s="",children:t,size:n=400}){return(0,e.jsx)(m,{className:s,spotlightColor:"rgba(33, 150, 243, 0.15)",size:n,children:t})}function N({className:s="",children:t,size:n=200}){return(0,e.jsx)(v,{className:s,size:n,children:t})}},d);export{j as __getNamedExports};
