{"permissions": {"allow": ["Bash(npx create-next-app:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "Bash(npm init:*)", "Bash(npm install:*)", "<PERSON><PERSON>(chmod:*)", "Bash(bash:*)", "WebFetch(domain:smartdomain.io)", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run build:*)", "Bash(rm:*)", "Bash(npx tsc:*)", "Bash(npm run type-check:*)", "<PERSON><PERSON>(curl:*)", "<PERSON>sh(node test-rdap.js)", "Bash(ss:*)", "Bash(node:*)", "WebFetch(domain:www.nazhumi.com)", "Bash(timeout 30 npm run dev)", "Bash(./test-api.sh:*)", "<PERSON><PERSON>(timeout:*)", "WebFetch(domain:wanwang.aliyun.com)", "WebFetch(domain:www.juming.com)", "WebFetch(domain:buy.cloud.tencent.com)", "WebFetch(domain:www.22.cn)", "WebFetch(domain:www.namecheap.com)", "WebFetch(domain:www.dynadot.com)", "WebFetch(domain:porkbun.com)", "Bash(pip3 install:*)", "<PERSON><PERSON>(python3:*)", "Bash(pip install:*)", "Bash(find:*)", "WebFetch(domain:data.iana.org)", "Bash(grep:*)", "Bash(NODE_ENV=production npm run build)", "Bash(git init:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git config:*)", "Bash(git branch:*)", "Bash(git push:*)", "Bash(git rm:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(nslookup:*)"], "deny": []}}