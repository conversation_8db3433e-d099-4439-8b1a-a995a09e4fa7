{"system": {"timestamp": "2025-07-16T15:08:49.858Z", "uptime": 7.215923547, "memory": {"rss": 74080256, "heapTotal": 23420928, "heapUsed": 16824648, "external": 3579632, "arrayBuffers": 75190}, "environment": "production"}, "cache": {"domain": {"hits": 0, "misses": 0, "sets": 0, "evictions": 0, "hitRate": 0, "size": 0, "maxSize": 1000, "info": {"hits": 0, "misses": 0, "sets": 0, "evictions": 0, "hitRate": 0, "size": 0, "maxSize": 1000, "memoryUsage": "0 B"}}, "search": {"hits": 0, "misses": 0, "sets": 0, "evictions": 0, "hitRate": 0, "size": 0, "maxSize": 1000, "info": {"hits": 0, "misses": 0, "sets": 0, "evictions": 0, "hitRate": 0, "size": 0, "maxSize": 1000, "memoryUsage": "0 B"}}, "tld": {"hits": 0, "misses": 0, "sets": 0, "evictions": 0, "hitRate": 0, "size": 0, "maxSize": 1000, "info": {"hits": 0, "misses": 0, "sets": 0, "evictions": 0, "hitRate": 0, "size": 0, "maxSize": 1000, "memoryUsage": "0 B"}}}, "performance": {"totalCacheSize": 0, "averageHitRate": 0, "totalHits": 0, "totalMisses": 0}, "recommendations": ["Domain cache hit rate is low - consider increasing TTL or pre-warming popular domains", "Search cache hit rate is low - consider caching search results longer", "TLD cache should have higher hit rate - check cache warming strategy"]}