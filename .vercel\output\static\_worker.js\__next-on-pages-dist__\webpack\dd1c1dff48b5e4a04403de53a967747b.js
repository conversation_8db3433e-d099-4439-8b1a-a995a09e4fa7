var ce={},nt=(Ke,ze,et)=>(ce.__chunk_26=(je,re,f)=>{f.d(re,{T:()=>U});var d=f(926);f(9220);var $=f(8277);let U=({className:m="",width:n=140,height:F=36,useWebP:J=!1})=>J?(0,d.jsx)("div",{className:`relative ${m}`,style:{width:n,height:F},children:(0,d.jsx)($.Z,{src:"/logo/nextname-logo.webp",alt:"NextName",width:n,height:F,priority:!0,className:"object-contain"})}):(0,d.jsxs)("svg",{width:n,height:F,viewBox:"0 0 140 36",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:m,children:[(0,d.jsxs)("g",{children:[(0,d.jsxs)("defs",{children:[(0,d.jsxs)("linearGradient",{id:"logoGradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#3B82F6"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#8B5CF6"})]}),(0,d.jsxs)("linearGradient",{id:"arrowGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,d.jsx)("stop",{offset:"0%",stopColor:"#10B981"}),(0,d.jsx)("stop",{offset:"100%",stopColor:"#3B82F6"})]})]}),(0,d.jsx)("circle",{cx:"18",cy:"18",r:"16",fill:"url(#logoGradient)",opacity:"0.1"}),(0,d.jsx)("path",{d:"M10 26V10L18 22V10M18 22L26 10V26",stroke:"url(#logoGradient)",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none"}),(0,d.jsx)("path",{d:"M22 18L26 18M26 18L24 16M26 18L24 20",stroke:"url(#arrowGradient)",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,d.jsx)("g",{children:(0,d.jsxs)("text",{x:"40",y:"24",fontFamily:"system-ui, -apple-system, sans-serif",fontSize:"18",fontWeight:"600",fill:"currentColor",children:["Next",(0,d.jsx)("tspan",{fill:"url(#logoGradient)",children:"Name"})]})})]})},ce.__chunk_8277=(je,re,f)=>{f.d(re,{Z:()=>Ge});var d=f(5354);function $(t){return t.default!==void 0}function U(t){return t===void 0?t:typeof t=="number"?Number.isFinite(t)?t:NaN:typeof t=="string"&&/^[0-9]+$/.test(t)?parseInt(t,10):NaN}var m=f(926),n=f(9220),F=f(8066);let J=()=>{},Ce=()=>{};function ke(t){var r;let{headManager:e,reduceComponentsToState:i}=t;function a(){if(e&&e.mountedInstances){let c=n.Children.toArray(Array.from(e.mountedInstances).filter(Boolean));e.updateHead(i(c,t))}}return e==null||(r=e.mountedInstances)==null||r.add(t.children),a(),J(()=>{var c;return e==null||(c=e.mountedInstances)==null||c.add(t.children),()=>{var s;e==null||(s=e.mountedInstances)==null||s.delete(t.children)}}),J(()=>(e&&(e._pendingUpdate=a),()=>{e&&(e._pendingUpdate=a)})),Ce(()=>(e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null),()=>{e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null)})),null}var _e=f(3130),Ee=f(3589);function Le(t,r){return typeof r=="string"||typeof r=="number"?t:r.type===n.Fragment?t.concat(n.Children.toArray(r.props.children).reduce((e,i)=>typeof i=="string"||typeof i=="number"?e:e.concat(i),[])):t.concat(r)}let ue=["name","httpEquiv","charSet","itemProp"];function Re(t,r){let{inAmpMode:e}=r;return t.reduce(Le,[]).reverse().concat(function(i){i===void 0&&(i=!1);let a=[(0,m.jsx)("meta",{charSet:"utf-8"})];return i||a.push((0,m.jsx)("meta",{name:"viewport",content:"width=device-width"})),a}(e).reverse()).filter(function(){let i=new Set,a=new Set,c=new Set,s={};return l=>{let u=!0,C=!1;if(l.key&&typeof l.key!="number"&&l.key.indexOf("$")>0){C=!0;let y=l.key.slice(l.key.indexOf("$")+1);i.has(y)?u=!1:i.add(y)}switch(l.type){case"title":case"base":a.has(l.type)?u=!1:a.add(l.type);break;case"meta":for(let y=0,R=ue.length;y<R;y++){let w=ue[y];if(l.props.hasOwnProperty(w))if(w==="charSet")c.has(w)?u=!1:c.add(w);else{let k=l.props[w],z=s[w]||new Set;(w!=="name"||!C)&&z.has(k)?u=!1:(z.add(k),s[w]=z)}}}return u}}()).reverse().map((i,a)=>{let c=i.key||a;if(!e&&i.type==="link"&&i.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(s=>i.props.href.startsWith(s))){let s={...i.props||{}};return s["data-href"]=s.href,s.href=void 0,s["data-optimized-fonts"]=!0,n.cloneElement(i,s)}return n.cloneElement(i,{key:c})})}let Me=function(t){let{children:r}=t,e=(0,n.useContext)(_e.AmpStateContext),i=(0,n.useContext)(Ee.HeadManagerContext);return(0,m.jsx)(ke,{reduceComponentsToState:Re,headManager:i,inAmpMode:function(a){let{ampFirst:c=!1,hybrid:s=!1,hasQuery:l=!1}=a===void 0?{}:a;return c||s&&l}(e),children:r})};var Ie=f(5918),Ne=f(402);function he(t){var r;let{config:e,src:i,width:a,quality:c}=t,s=c||((r=e.qualities)==null?void 0:r.reduce((l,u)=>Math.abs(u-75)<Math.abs(l-75)?u:l))||75;return e.path+"?url="+encodeURIComponent(i)+"&w="+a+"&q="+s}he.__next_img_default=!0;let Pe={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function pe(t,r,e,i,a,c,s){let l=t?.src;t&&t["data-loaded-src"]!==l&&(t["data-loaded-src"]=l,("decode"in t?t.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(t.parentElement&&t.isConnected){if(r!=="empty"&&a(!0),e?.current){let u=new Event("load");Object.defineProperty(u,"target",{writable:!1,value:t});let C=!1,y=!1;e.current({...u,nativeEvent:u,currentTarget:t,target:t,isDefaultPrevented:()=>C,isPropagationStopped:()=>y,persist:()=>{},preventDefault:()=>{C=!0,u.preventDefault()},stopPropagation:()=>{y=!0,u.stopPropagation()}})}i?.current&&i.current(t)}}))}function fe(t){return n.use?{fetchPriority:t}:{fetchpriority:t}}ze.__NEXT_IMAGE_IMPORTED=!0;let Ae=(0,n.forwardRef)((t,r)=>{let{src:e,srcSet:i,sizes:a,height:c,width:s,decoding:l,className:u,style:C,fetchPriority:y,placeholder:R,loading:w,unoptimized:k,fill:z,onLoadRef:M,onLoadingCompleteRef:S,setBlurComplete:_,setShowAltText:P,sizesInput:x,onLoad:E,onError:v,...g}=t;return(0,m.jsx)("img",{...g,...fe(y),loading:w,width:s,height:c,decoding:l,"data-nimg":z?"fill":"1",className:u,style:C,sizes:a,srcSet:i,src:e,ref:(0,n.useCallback)(p=>{r&&(typeof r=="function"?r(p):typeof r=="object"&&(r.current=p)),p&&(v&&(p.src=p.src),p.complete&&pe(p,R,M,S,_,k,x))},[e,R,M,S,_,v,k,x,r]),onLoad:p=>{pe(p.currentTarget,R,M,S,_,k,x)},onError:p=>{P(!0),R!=="empty"&&_(!0),v&&v(p)}})});function Fe(t){let{isAppRouter:r,imgAttributes:e}=t,i={as:"image",imageSrcSet:e.srcSet,imageSizes:e.sizes,crossOrigin:e.crossOrigin,referrerPolicy:e.referrerPolicy,...fe(e.fetchPriority)};return r&&F.preload?(F.preload(e.src,i),null):(0,m.jsx)(Me,{children:(0,m.jsx)("link",{rel:"preload",href:e.srcSet?void 0:e.src,...i},"__nimg-"+e.src+e.srcSet+e.sizes)})}let Ge=(0,n.forwardRef)((t,r)=>{let e=(0,n.useContext)(Ne.RouterContext),i=(0,n.useContext)(Ie.ImageConfigContext),a=(0,n.useMemo)(()=>{var M;let S=Pe||i||d.z,_=[...S.deviceSizes,...S.imageSizes].sort((E,v)=>E-v),P=S.deviceSizes.sort((E,v)=>E-v),x=(M=S.qualities)==null?void 0:M.sort((E,v)=>E-v);return{...S,allSizes:_,deviceSizes:P,qualities:x}},[i]),{onLoad:c,onLoadingComplete:s}=t,l=(0,n.useRef)(c);(0,n.useEffect)(()=>{l.current=c},[c]);let u=(0,n.useRef)(s);(0,n.useEffect)(()=>{u.current=s},[s]);let[C,y]=(0,n.useState)(!1),[R,w]=(0,n.useState)(!1),{props:k,meta:z}=function(M,S){var _,P;let x,E,v,{src:g,sizes:p,unoptimized:W=!1,priority:oe=!1,loading:ne,className:qe,quality:Be,width:Ue,height:We,fill:Z=!1,style:se,overrideSrc:Oe,onLoad:tt,onLoadingComplete:it,placeholder:Y="empty",blurDataURL:le,fetchPriority:ge,decoding:Te="async",layout:Q,objectFit:De,objectPosition:He,lazyBoundary:rt,lazyRoot:ot,...X}=M,{imgConf:Ve,showAltText:$e,blurComplete:Je,defaultLoader:me}=S,A=Ve||d.z;if("allSizes"in A)x=A;else{let o=[...A.deviceSizes,...A.imageSizes].sort((b,j)=>b-j),h=A.deviceSizes.sort((b,j)=>b-j),N=(_=A.qualities)==null?void 0:_.sort((b,j)=>b-j);x={...A,allSizes:o,deviceSizes:h,qualities:N}}if(me===void 0)throw Error(`images.loaderFile detected but the file is missing default export.
Read more: https://nextjs.org/docs/messages/invalid-images-config`);let K=X.loader||me;delete X.loader,delete X.srcSet;let ye="__next_img_default"in K;if(ye){if(x.loader==="custom")throw Error('Image with src "'+g+`" is missing "loader" prop.
Read more: https://nextjs.org/docs/messages/next-image-missing-loader`)}else{let o=K;K=h=>{let{config:N,...b}=h;return o(b)}}if(Q){Q==="fill"&&(Z=!0);let o={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[Q];o&&(se={...se,...o});let h={responsive:"100vw",fill:"100vw"}[Q];h&&!p&&(p=h)}let ve="",L=U(Ue),I=U(We);if(typeof(P=g)=="object"&&($(P)||P.src!==void 0)){let o=$(g)?g.default:g;if(!o.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(o));if(!o.height||!o.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(o));if(E=o.blurWidth,v=o.blurHeight,le=le||o.blurDataURL,ve=o.src,!Z)if(L||I){if(L&&!I){let h=L/o.width;I=Math.round(o.height*h)}else if(!L&&I){let h=I/o.height;L=Math.round(o.width*h)}}else L=o.width,I=o.height}let we=!oe&&(ne==="lazy"||ne===void 0);(!(g=typeof g=="string"?g:ve)||g.startsWith("data:")||g.startsWith("blob:"))&&(W=!0,we=!1),x.unoptimized&&(W=!0),ye&&g.endsWith(".svg")&&!x.dangerouslyAllowSVG&&(W=!0),oe&&(ge="high");let Ze=U(Be),ee=Object.assign(Z?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:De,objectPosition:He}:{},$e?{}:{color:"transparent"},se),xe=Je||Y==="empty"?null:Y==="blur"?'url("data:image/svg+xml;charset=utf-8,'+function(o){let{widthInt:h,heightInt:N,blurWidth:b,blurHeight:j,blurDataURL:te,objectFit:G}=o,O=b?40*b:h,q=j?40*j:N,B=O&&q?"viewBox='0 0 "+O+" "+q+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+B+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(B?"none":G==="contain"?"xMidYMid":G==="cover"?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+te+"'/%3E%3C/svg%3E"}({widthInt:L,heightInt:I,blurWidth:E,blurHeight:v,blurDataURL:le||"",objectFit:ee.objectFit})+'")':'url("'+Y+'")',Ye=xe?{backgroundSize:ee.objectFit||"cover",backgroundPosition:ee.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:xe}:{},ae=function(o){let{config:h,src:N,unoptimized:b,width:j,quality:te,sizes:G,loader:O}=o;if(b)return{src:N,srcSet:void 0,sizes:void 0};let{widths:q,kind:B}=function(ie,T,be){let{deviceSizes:Se,allSizes:D}=ie;if(be){let de=/(^|\s)(1?\d?\d)vw/g,H=[];for(let V;V=de.exec(be);V)H.push(parseInt(V[2]));if(H.length){let V=.01*Math.min(...H);return{widths:D.filter(Xe=>Xe>=Se[0]*V),kind:"w"}}return{widths:D,kind:"w"}}return typeof T!="number"?{widths:Se,kind:"w"}:{widths:[...new Set([T,2*T].map(de=>D.find(H=>H>=de)||D[D.length-1]))],kind:"x"}}(h,j,G),Qe=q.length-1;return{sizes:G||B!=="w"?G:"100vw",srcSet:q.map((ie,T)=>O({config:h,src:N,quality:te,width:ie})+" "+(B==="w"?ie:T+1)+B).join(", "),src:O({config:h,src:N,quality:te,width:q[Qe]})}}({config:x,src:g,unoptimized:W,width:L,quality:Ze,sizes:p,loader:K});return{props:{...X,loading:we?"lazy":ne,fetchPriority:ge,width:L,height:I,decoding:Te,className:qe,style:{...ee,...Ye},sizes:ae.sizes,srcSet:ae.srcSet,src:Oe||ae.src},meta:{unoptimized:W,priority:oe,placeholder:Y,fill:Z}}}(t,{defaultLoader:he,imgConf:a,blurComplete:C,showAltText:R});return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(Ae,{...k,unoptimized:z.unoptimized,placeholder:z.placeholder,fill:z.fill,onLoadRef:l,onLoadingCompleteRef:u,setBlurComplete:y,setShowAltText:w,sizesInput:t.sizes,ref:r}),z.priority?(0,m.jsx)(Fe,{isAppRouter:!e,imgAttributes:k}):null]})})},ce);export{nt as __getNamedExports};
