"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[293],{1293:function(e,t,n){n.r(t),n.d(t,{BestNameSpotlight:function(){return l},CardSpotlight:function(){return c},FramerSpotlight:function(){return i},HeroSpotlight:function(){return u},LightSpotlight:function(){return o}});var r=n(7437),a=n(2265),s=n(429);function i(e){let{className:t="",children:n,spotlightColor:i="rgba(20, 128, 255, 0.15)",size:o=300}=e,l=(0,a.useRef)(null),[c,u]=(0,a.useState)({x:0,y:0}),[v,d]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=l.current;if(!e)return;let t=t=>{let n=e.getBoundingClientRect();u({x:t.clientX-n.left,y:t.clientY-n.top})},n=()=>d(!0),r=()=>d(!1);return e.addEventListener("mousemove",t,{passive:!0}),e.addEventListener("mouseenter",n),e.addEventListener("mouseleave",r),()=>{e.removeEventListener("mousemove",t),e.removeEventListener("mouseenter",n),e.removeEventListener("mouseleave",r)}},[]),(0,r.jsxs)("div",{ref:l,className:"relative overflow-hidden ".concat(t),style:{isolation:"isolate"},children:[(0,r.jsx)(s.E.div,{className:"pointer-events-none absolute inset-0 z-0",animate:{background:v?"radial-gradient(".concat(o,"px circle at ").concat(c.x,"px ").concat(c.y,"px, ").concat(i,", transparent 70%)"):"transparent"},transition:{background:{duration:.2,ease:"easeOut"}}}),(0,r.jsx)("div",{className:"relative z-10",children:n})]})}function o(e){let{className:t="",children:n,size:i=200}=e,o=(0,a.useRef)(null),[l,c]=(0,a.useState)({x:0,y:0}),[u,v]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=o.current;if(!e)return;let t=t=>{let n=e.getBoundingClientRect();c({x:t.clientX-n.left,y:t.clientY-n.top})},n=()=>v(!0),r=()=>v(!1);return e.addEventListener("mousemove",t,{passive:!0}),e.addEventListener("mouseenter",n),e.addEventListener("mouseleave",r),()=>{e.removeEventListener("mousemove",t),e.removeEventListener("mouseenter",n),e.removeEventListener("mouseleave",r)}},[]),(0,r.jsxs)("div",{ref:o,className:"relative overflow-hidden ".concat(t),style:{isolation:"isolate"},children:[(0,r.jsx)(s.E.div,{className:"pointer-events-none absolute inset-0 z-0",animate:{background:u?"radial-gradient(".concat(i,"px circle at ").concat(l.x,"px ").concat(l.y,"px, rgba(33, 150, 243, 0.08), transparent 60%)"):"transparent"},transition:{background:{duration:.3,ease:"easeOut"}}}),(0,r.jsx)("div",{className:"relative z-10",children:n})]})}function l(e){let{className:t="",children:n,size:a=400}=e;return(0,r.jsx)(i,{className:t,spotlightColor:"rgba(33, 150, 243, 0.15)",size:a,children:n})}function c(e){let{className:t="",children:n,size:a=200}=e;return(0,r.jsx)(o,{className:t,size:a,children:n})}function u(e){let{className:t="",children:n,size:i=600}=e,o=(0,a.useRef)(null),[l,c]=(0,a.useState)({x:0,y:0}),[u,v]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e;let t=o.current;if(!t)return;let n=n=>{e&&cancelAnimationFrame(e),e=requestAnimationFrame(()=>{let e=t.getBoundingClientRect();c({x:n.clientX-e.left,y:n.clientY-e.top})})},r=()=>v(!0),a=()=>v(!1);return t.addEventListener("mousemove",n,{passive:!0}),t.addEventListener("mouseenter",r),t.addEventListener("mouseleave",a),()=>{e&&cancelAnimationFrame(e),t.removeEventListener("mousemove",n),t.removeEventListener("mouseenter",r),t.removeEventListener("mouseleave",a)}},[]),(0,r.jsxs)("div",{ref:o,className:"relative overflow-hidden ".concat(t),style:{isolation:"isolate"},children:[(0,r.jsx)(s.E.div,{className:"pointer-events-none absolute inset-0 z-0",animate:{background:u?"radial-gradient(".concat(i,"px circle at ").concat(l.x,"px ").concat(l.y,"px, rgba(33, 150, 243, 0.15), transparent 70%)"):"transparent"},transition:{background:{duration:.2,ease:"easeOut"}}}),(0,r.jsx)("div",{className:"relative z-10",children:n})]})}}}]);