{"version": 3, "routes": [{"src": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$", "headers": {"Location": "/$1"}, "status": 308, "missing": [{"type": "header", "key": "x-nextjs-data"}], "continue": true}, {"src": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$", "headers": {"Location": "/$1/"}, "status": 308, "continue": true}, {"src": "/_next/__private/trace", "dest": "/404", "status": 404, "continue": true}, {"src": "^/api/tlds(?:/)?$", "headers": {"Cache-Control": "public, s-maxage=86400, stale-while-revalidate=43200"}, "continue": true}, {"src": "^/api/domain(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$", "headers": {"Cache-Control": "public, s-maxage=300, stale-while-revalidate=600"}, "continue": true}, {"src": "/404/?", "status": 404, "continue": true, "missing": [{"type": "header", "key": "x-prerender-revalidate"}]}, {"src": "/500", "status": 500, "continue": true}, {"src": "^/?", "has": [{"type": "header", "key": "rsc"}], "dest": "/index.rsc", "headers": {"vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch"}, "continue": true, "override": true}, {"src": "^/((?!.+\\.rsc).+?)(?:/)?$", "has": [{"type": "header", "key": "rsc"}], "dest": "/$1.rsc", "headers": {"vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch"}, "continue": true, "override": true}, {"handle": "filesystem"}, {"src": "/index(\\.action|\\.rsc)", "dest": "/", "continue": true}, {"src": "/_next/data/(.*)", "dest": "/_next/data/$1", "check": true}, {"src": "/\\.prefetch\\.rsc$", "dest": "/__index.prefetch.rsc", "check": true}, {"src": "/(.+)/\\.prefetch\\.rsc$", "dest": "/$1.prefetch.rsc", "check": true}, {"src": "/\\.rsc$", "dest": "/index.rsc", "check": true}, {"src": "/(.+)/\\.rsc$", "dest": "/$1.rsc", "check": true}, {"handle": "resource"}, {"src": "/.*", "status": 404}, {"handle": "miss"}, {"src": "/_next/static/(?:[^/]+/pages|pages|chunks|runtime|css|image|media)/.+", "status": 404, "check": true, "dest": "$0"}, {"handle": "rewrite"}, {"src": "/_next/data/(.*)", "dest": "/404", "status": 404}, {"src": "^/api/domain/(?<nxtPdomain>[^/]+?)(?:\\.rsc)(?:/)?$", "dest": "/api/domain/[domain].rsc?nxtPdomain=$nxtPdomain"}, {"src": "^/api/domain/(?<nxtPdomain>[^/]+?)(?:/)?$", "dest": "/api/domain/[domain]?nxtPdomain=$nxtPdomain"}, {"src": "^/domain/(?<nxtPdomain>[^/]+?)(?:\\.rsc)(?:/)?$", "dest": "/domain/[domain].rsc?nxtPdomain=$nxtPdomain"}, {"src": "^/domain/(?<nxtPdomain>[^/]+?)(?:/)?$", "dest": "/domain/[domain]?nxtPdomain=$nxtPdomain"}, {"handle": "hit"}, {"src": "/_next/static/(?:[^/]+/pages|pages|chunks|runtime|css|image|media|L1ERtjXbxPN4Alx9vZK5J)/.+", "headers": {"cache-control": "public,max-age=31536000,immutable"}, "continue": true, "important": true}, {"src": "/index(?:/)?", "headers": {"x-matched-path": "/"}, "continue": true, "important": true}, {"src": "/((?!index$).*?)(?:/)?", "headers": {"x-matched-path": "/$1"}, "continue": true, "important": true}, {"handle": "error"}, {"src": "/.*", "dest": "/_not-found", "status": 404}, {"src": "/.*", "dest": "/500", "status": 500}], "images": {"domains": [], "sizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840, 16, 32, 48, 64, 96, 128, 256, 384], "remotePatterns": [], "minimumCacheTTL": 604800, "formats": ["image/webp", "image/avif"], "dangerouslyAllowSVG": false, "contentSecurityPolicy": "script-src 'none'; frame-src 'none'; sandbox;", "contentDispositionType": "inline"}, "overrides": {"500.html": {"path": "500", "contentType": "text/html; charset=utf-8"}, "_app.rsc.json": {"path": "_app.rsc", "contentType": "application/json"}, "_error.rsc.json": {"path": "_error.rsc", "contentType": "application/json"}, "_document.rsc.json": {"path": "_document.rsc", "contentType": "application/json"}}, "framework": {"version": "14.2.30"}, "crons": []}